# My Shop Frontend - Next.js E-commerce Application

A modern, responsive e-commerce frontend built with Next.js 14, featuring a beautiful UI, seamless shopping experience, and comprehensive user management.

## 🚀 Features

- **Modern UI/UX**: Clean, responsive design with Tailwind CSS
- **Product Catalog**: Browse, search, and filter products
- **Shopping Cart**: Add, update, and manage cart items
- **User Authentication**: Secure login/register system
- **Order Management**: Place orders and track order history
- **Profile Management**: Update user information and preferences
- **Admin Interface**: Product and order management (for admin users)
- **Mobile Responsive**: Optimized for all device sizes
- **Performance Optimized**: Fast loading with Next.js optimizations

## 📋 Prerequisites

- Node.js 18+ 
- npm or yarn
- Backend API running on http://localhost:8000

## 🛠 Installation

1. **Install dependencies**:
   ```bash
   npm install
   # or
   yarn install
   ```

2. **Configure environment**:
   Create `.env.local` file:
   ```env
   NEXT_PUBLIC_API_URL=http://localhost:8000
   NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key
   ```

3. **Start development server**:
   ```bash
   npm run dev
   # or
   yarn dev
   ```

The application will be available at: http://localhost:3000

## 📁 Project Structure

```
fe/
├── src/
│   ├── app/                    # App Router (Next.js 14)
│   │   ├── auth/              # Authentication pages
│   │   │   ├── login/         # Login page
│   │   │   └── register/      # Registration page
│   │   ├── products/          # Product pages
│   │   ├── cart/              # Shopping cart page
│   │   ├── checkout/          # Checkout process
│   │   ├── profile/           # User profile
│   │   ├── orders/            # Order history
│   │   ├── admin/             # Admin panel
│   │   ├── globals.css        # Global styles
│   │   ├── layout.tsx         # Root layout
│   │   └── page.tsx           # Home page
│   ├── components/            # Reusable components
│   │   ├── layout/            # Layout components
│   │   │   ├── Navbar.tsx     # Navigation bar
│   │   │   └── Footer.tsx     # Footer
│   │   ├── home/              # Home page components
│   │   │   ├── Hero.tsx       # Hero section
│   │   │   ├── FeaturedProducts.tsx
│   │   │   ├── Categories.tsx
│   │   │   └── Newsletter.tsx
│   │   ├── products/          # Product components
│   │   │   ├── ProductCard.tsx
│   │   │   └── ProductFilters.tsx
│   │   └── ui/                # UI components
│   ├── lib/                   # Utilities and configurations
│   │   ├── api.ts             # API client
│   │   ├── auth-context.tsx   # Authentication context
│   │   ├── cart-context.tsx   # Shopping cart context
│   │   └── utils.ts           # Utility functions
│   └── types/                 # TypeScript type definitions
│       └── index.ts           # Type definitions
├── public/                    # Static assets
├── next.config.js             # Next.js configuration
├── tailwind.config.js         # Tailwind CSS configuration
├── postcss.config.js          # PostCSS configuration
└── package.json               # Dependencies and scripts
```

## 🎨 Design System

### Colors
- **Primary**: Blue (#3B82F6)
- **Secondary**: Gray (#6B7280)
- **Success**: Green (#10B981)
- **Warning**: Yellow (#F59E0B)
- **Error**: Red (#EF4444)

### Typography
- **Font**: Inter (Google Fonts)
- **Headings**: Bold weights (600-800)
- **Body**: Regular weight (400)
- **Small text**: Medium weight (500)

### Components
- **Buttons**: Consistent styling with hover states
- **Forms**: Clean input fields with validation
- **Cards**: Subtle shadows and rounded corners
- **Navigation**: Sticky header with mobile menu

## 🔧 Key Components

### Authentication System
```tsx
// Login/Register forms with validation
// JWT token management
// Protected routes
// User context provider
```

### Product Catalog
```tsx
// Product grid with filtering
// Search functionality
// Category navigation
// Product detail pages
```

### Shopping Cart
```tsx
// Add/remove items
// Quantity updates
// Price calculations
// Persistent storage
```

### Order Management
```tsx
// Checkout process
// Order confirmation
// Order history
// Status tracking
```

## 🌐 API Integration

The frontend communicates with the FastAPI backend through:

### API Client (`lib/api.ts`)
```typescript
// Axios-based HTTP client
// Automatic token attachment
// Error handling
// Request/response interceptors
```

### Context Providers
- **AuthContext**: User authentication state
- **CartContext**: Shopping cart management
- **Global state management with React Context**

### API Endpoints Used
- Authentication: `/api/v1/auth/*`
- Products: `/api/v1/products/*`
- Cart: `/api/v1/cart/*`
- Orders: `/api/v1/orders/*`
- Admin: `/api/v1/admin/*`

## 📱 Responsive Design

### Breakpoints
- **Mobile**: < 640px
- **Tablet**: 640px - 1024px
- **Desktop**: > 1024px

### Mobile Features
- Hamburger menu navigation
- Touch-friendly buttons
- Optimized product grid
- Mobile-first design approach

## 🔐 Authentication Flow

1. **Registration**: Create account with email/username
2. **Login**: Authenticate with credentials
3. **Token Storage**: JWT stored in secure cookies
4. **Auto-refresh**: Token validation on page load
5. **Logout**: Clear tokens and redirect

## 🛒 Shopping Experience

### Product Discovery
- Featured products on homepage
- Category-based browsing
- Search with filters
- Product recommendations

### Cart Management
- Add to cart from product pages
- Update quantities in cart
- Remove items
- Price calculations with discounts

### Checkout Process
- Shipping address form
- Payment method selection
- Order review and confirmation
- Order success page

## 👤 User Features

### Profile Management
- Update personal information
- Change password
- View order history
- Manage addresses

### Order Tracking
- Order status updates
- Detailed order information
- Reorder functionality
- Order cancellation (if applicable)

## 👨‍💼 Admin Features

### Product Management
- Add new products
- Edit existing products
- Manage inventory
- Upload product images

### Order Management
- View all orders
- Update order status
- Process refunds
- Generate reports

### Analytics Dashboard
- Sales metrics
- Popular products
- Customer insights
- Revenue tracking

## 🎯 Performance Optimizations

### Next.js Features
- **App Router**: Latest Next.js routing system
- **Image Optimization**: Automatic image optimization
- **Code Splitting**: Automatic code splitting
- **Static Generation**: Pre-rendered pages where possible

### Loading States
- Skeleton screens for loading content
- Optimistic updates for better UX
- Error boundaries for error handling
- Lazy loading for images and components

## 🧪 Development

### Available Scripts
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # TypeScript type checking
```

### Code Quality
- **ESLint**: Code linting
- **Prettier**: Code formatting
- **TypeScript**: Type safety
- **Husky**: Git hooks (optional)

## 🚀 Deployment

### Build Process
```bash
npm run build
```

### Deployment Options
- **Vercel**: Recommended for Next.js apps
- **Netlify**: Static site hosting
- **AWS**: S3 + CloudFront
- **Docker**: Containerized deployment

### Environment Variables
```env
NEXT_PUBLIC_API_URL=https://api.yoursite.com
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_...
```

## 🔍 SEO Optimization

### Meta Tags
- Dynamic page titles
- Product descriptions
- Open Graph tags
- Twitter Card tags

### Structured Data
- Product schema markup
- Breadcrumb navigation
- Organization information
- Review ratings (when implemented)

## 🎨 Customization

### Theming
- Modify `tailwind.config.js` for colors
- Update CSS custom properties
- Customize component styles
- Add new design tokens

### Adding Features
- Create new components in `components/`
- Add new pages in `app/`
- Extend API client in `lib/api.ts`
- Update type definitions in `types/`

## 🐛 Troubleshooting

### Common Issues
1. **API Connection**: Check backend server is running
2. **Authentication**: Verify JWT token storage
3. **CORS Errors**: Check backend CORS configuration
4. **Build Errors**: Ensure all dependencies are installed

### Debug Mode
```bash
# Enable debug logging
DEBUG=* npm run dev
```

## 🤝 Contributing

1. Follow the existing code style
2. Use TypeScript for all new code
3. Add proper error handling
4. Test on multiple devices
5. Update documentation

## 📞 Support

For issues and questions:
1. Check browser console for errors
2. Verify API connectivity
3. Review component props and state
4. Check network requests in DevTools
