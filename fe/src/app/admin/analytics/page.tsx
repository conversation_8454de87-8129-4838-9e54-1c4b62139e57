'use client'

import { useState, useEffect } from 'react'
import { formatPrice } from '@/lib/utils'
import { 
  ChartBarIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  CurrencyDollarIcon,
  ShoppingBagIcon,
  UsersIcon,
  EyeIcon
} from '@heroicons/react/24/outline'

interface AnalyticsData {
  revenue: {
    total: number
    change: number
    trend: 'up' | 'down'
  }
  orders: {
    total: number
    change: number
    trend: 'up' | 'down'
  }
  customers: {
    total: number
    change: number
    trend: 'up' | 'down'
  }
  pageViews: {
    total: number
    change: number
    trend: 'up' | 'down'
  }
}

export default function AdminAnalyticsPage() {
  const [analytics, setAnalytics] = useState<AnalyticsData>({
    revenue: { total: 0, change: 0, trend: 'up' },
    orders: { total: 0, change: 0, trend: 'up' },
    customers: { total: 0, change: 0, trend: 'up' },
    pageViews: { total: 0, change: 0, trend: 'up' }
  })
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState('7d')

  useEffect(() => {
    fetchAnalytics()
  }, [timeRange])

  const fetchAnalytics = async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      setAnalytics({
        revenue: { total: 45678.90, change: 12.5, trend: 'up' },
        orders: { total: 342, change: 8.2, trend: 'up' },
        customers: { total: 1250, change: -2.1, trend: 'down' },
        pageViews: { total: 15420, change: 15.7, trend: 'up' }
      })
    } catch (error) {
      console.error('Failed to fetch analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  const metrics = [
    {
      name: 'Total Revenue',
      value: formatPrice(analytics.revenue.total),
      change: analytics.revenue.change,
      trend: analytics.revenue.trend,
      icon: CurrencyDollarIcon,
      color: 'bg-green-500'
    },
    {
      name: 'Total Orders',
      value: analytics.orders.total.toLocaleString(),
      change: analytics.orders.change,
      trend: analytics.orders.trend,
      icon: ShoppingBagIcon,
      color: 'bg-blue-500'
    },
    {
      name: 'Total Customers',
      value: analytics.customers.total.toLocaleString(),
      change: analytics.customers.change,
      trend: analytics.customers.trend,
      icon: UsersIcon,
      color: 'bg-purple-500'
    },
    {
      name: 'Page Views',
      value: analytics.pageViews.total.toLocaleString(),
      change: analytics.pageViews.change,
      trend: analytics.pageViews.trend,
      icon: EyeIcon,
      color: 'bg-yellow-500'
    }
  ]

  const topProducts = [
    { name: 'Wireless Headphones', sales: 145, revenue: 21675.00 },
    { name: 'Smartphone Case', sales: 89, revenue: 2670.00 },
    { name: 'Laptop Stand', sales: 67, revenue: 4020.00 },
    { name: 'Coffee Mug', sales: 234, revenue: 3510.00 },
    { name: 'Desk Lamp', sales: 45, revenue: 2250.00 }
  ]

  const recentActivity = [
    { type: 'order', message: 'New order #1234 placed', time: '2 minutes ago' },
    { type: 'user', message: 'New user registered', time: '5 minutes ago' },
    { type: 'product', message: 'Product "Wireless Mouse" updated', time: '10 minutes ago' },
    { type: 'order', message: 'Order #1233 shipped', time: '15 minutes ago' },
    { type: 'review', message: 'New review on "Laptop Stand"', time: '20 minutes ago' }
  ]

  if (loading) {
    return (
      <div className="px-4 sm:px-6 lg:px-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-300 rounded w-48 mb-8"></div>
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-white overflow-hidden shadow rounded-lg p-5">
                <div className="h-4 bg-gray-300 rounded mb-4"></div>
                <div className="h-8 bg-gray-300 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="mb-8 flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Analytics</h1>
          <p className="mt-1 text-sm text-gray-500">
            Track your store's performance and key metrics.
          </p>
        </div>
        
        <div className="flex items-center space-x-4">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="24h">Last 24 hours</option>
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
          </select>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        {metrics.map((metric) => {
          const Icon = metric.icon
          return (
            <div key={metric.name} className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className={`${metric.color} p-3 rounded-md`}>
                      <Icon className="h-6 w-6 text-white" />
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        {metric.name}
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {metric.value}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-5 py-3">
                <div className="text-sm">
                  <span className={`font-medium ${
                    metric.trend === 'up' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {metric.trend === 'up' ? (
                      <TrendingUpIcon className="inline h-4 w-4 mr-1" />
                    ) : (
                      <TrendingDownIcon className="inline h-4 w-4 mr-1" />
                    )}
                    {Math.abs(metric.change)}%
                  </span>
                  <span className="text-gray-500"> from last period</span>
                </div>
              </div>
            </div>
          )
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Revenue Chart Placeholder */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            Revenue Overview
          </h3>
          <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <ChartBarIcon className="h-12 w-12 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500">Revenue chart would be displayed here</p>
              <p className="text-sm text-gray-400">Integration with chart library needed</p>
            </div>
          </div>
        </div>

        {/* Orders Chart Placeholder */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            Orders Overview
          </h3>
          <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <ChartBarIcon className="h-12 w-12 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500">Orders chart would be displayed here</p>
              <p className="text-sm text-gray-400">Integration with chart library needed</p>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Top Products */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Top Products
            </h3>
            <div className="flow-root">
              <ul className="-my-5 divide-y divide-gray-200">
                {topProducts.map((product, index) => (
                  <li key={product.name} className="py-4">
                    <div className="flex items-center space-x-4">
                      <div className="flex-shrink-0">
                        <span className="inline-flex items-center justify-center h-8 w-8 rounded-full bg-primary-100 text-primary-800 text-sm font-medium">
                          {index + 1}
                        </span>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {product.name}
                        </p>
                        <p className="text-sm text-gray-500">
                          {product.sales} sales
                        </p>
                      </div>
                      <div className="text-sm font-medium text-gray-900">
                        {formatPrice(product.revenue)}
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Recent Activity
            </h3>
            <div className="flow-root">
              <ul className="-my-5 divide-y divide-gray-200">
                {recentActivity.map((activity, index) => (
                  <li key={index} className="py-4">
                    <div className="flex space-x-3">
                      <div className="flex-shrink-0">
                        <div className={`h-8 w-8 rounded-full flex items-center justify-center ${
                          activity.type === 'order' ? 'bg-blue-100' :
                          activity.type === 'user' ? 'bg-green-100' :
                          activity.type === 'product' ? 'bg-yellow-100' :
                          'bg-purple-100'
                        }`}>
                          {activity.type === 'order' && <ShoppingBagIcon className="h-4 w-4 text-blue-600" />}
                          {activity.type === 'user' && <UsersIcon className="h-4 w-4 text-green-600" />}
                          {activity.type === 'product' && <ChartBarIcon className="h-4 w-4 text-yellow-600" />}
                          {activity.type === 'review' && <EyeIcon className="h-4 w-4 text-purple-600" />}
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm text-gray-900">
                          {activity.message}
                        </p>
                        <p className="text-sm text-gray-500">
                          {activity.time}
                        </p>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
