'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import toast from 'react-hot-toast'
import { 
  CogIcon,
  ShieldCheckIcon,
  CurrencyDollarIcon,
  TruckIcon,
  EnvelopeIcon,
  GlobeAltIcon
} from '@heroicons/react/24/outline'

interface StoreSettings {
  storeName: string
  storeDescription: string
  storeEmail: string
  storePhone: string
  storeAddress: string
  currency: string
  taxRate: number
  shippingRate: number
  freeShippingThreshold: number
}

interface NotificationSettings {
  emailNotifications: boolean
  orderNotifications: boolean
  lowStockNotifications: boolean
  newUserNotifications: boolean
}

export default function AdminSettingsPage() {
  const [activeTab, setActiveTab] = useState('general')
  const [isUpdating, setIsUpdating] = useState(false)

  const {
    register: registerStore,
    handleSubmit: handleSubmitStore,
    formState: { errors: storeErrors }
  } = useForm<StoreSettings>({
    defaultValues: {
      storeName: 'My Shop',
      storeDescription: 'Your one-stop shop for everything you need',
      storeEmail: '<EMAIL>',
      storePhone: '+****************',
      storeAddress: '123 Commerce Street, Business District, New York, NY 10001',
      currency: 'USD',
      taxRate: 10,
      shippingRate: 9.99,
      freeShippingThreshold: 50
    }
  })

  const {
    register: registerNotifications,
    handleSubmit: handleSubmitNotifications,
    watch: watchNotifications
  } = useForm<NotificationSettings>({
    defaultValues: {
      emailNotifications: true,
      orderNotifications: true,
      lowStockNotifications: true,
      newUserNotifications: false
    }
  })

  const onSubmitStore = async (data: StoreSettings) => {
    setIsUpdating(true)
    try {
      // This would call admin API to update store settings
      // await adminAPI.updateStoreSettings(data)
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast.success('Store settings updated successfully!')
    } catch (error) {
      toast.error('Failed to update store settings')
    } finally {
      setIsUpdating(false)
    }
  }

  const onSubmitNotifications = async (data: NotificationSettings) => {
    setIsUpdating(true)
    try {
      // This would call admin API to update notification settings
      // await adminAPI.updateNotificationSettings(data)
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast.success('Notification settings updated successfully!')
    } catch (error) {
      toast.error('Failed to update notification settings')
    } finally {
      setIsUpdating(false)
    }
  }

  const tabs = [
    { id: 'general', name: 'General', icon: CogIcon },
    { id: 'payments', name: 'Payments', icon: CurrencyDollarIcon },
    { id: 'shipping', name: 'Shipping', icon: TruckIcon },
    { id: 'notifications', name: 'Notifications', icon: EnvelopeIcon },
    { id: 'security', name: 'Security', icon: ShieldCheckIcon },
    { id: 'integrations', name: 'Integrations', icon: GlobeAltIcon }
  ]

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
        <p className="mt-1 text-sm text-gray-500">
          Manage your store configuration and preferences.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <nav className="space-y-1">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                    activeTab === tab.id
                      ? 'bg-primary-100 text-primary-700'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <Icon className="h-5 w-5 mr-3" />
                  {tab.name}
                </button>
              )
            })}
          </nav>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3">
          <div className="bg-white shadow rounded-lg p-6">
            {/* General Settings */}
            {activeTab === 'general' && (
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-6">General Settings</h2>
                
                <form onSubmit={handleSubmitStore(onSubmitStore)} className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Store Name
                    </label>
                    <input
                      {...registerStore('storeName', { required: 'Store name is required' })}
                      type="text"
                      className="input-field"
                    />
                    {storeErrors.storeName && (
                      <p className="mt-1 text-sm text-red-600">{storeErrors.storeName.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Store Description
                    </label>
                    <textarea
                      {...registerStore('storeDescription')}
                      rows={3}
                      className="input-field"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Store Email
                      </label>
                      <input
                        {...registerStore('storeEmail', {
                          required: 'Email is required',
                          pattern: {
                            value: /^\S+@\S+$/i,
                            message: 'Invalid email address'
                          }
                        })}
                        type="email"
                        className="input-field"
                      />
                      {storeErrors.storeEmail && (
                        <p className="mt-1 text-sm text-red-600">{storeErrors.storeEmail.message}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Store Phone
                      </label>
                      <input
                        {...registerStore('storePhone')}
                        type="tel"
                        className="input-field"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Store Address
                    </label>
                    <textarea
                      {...registerStore('storeAddress')}
                      rows={3}
                      className="input-field"
                    />
                  </div>

                  <div>
                    <button
                      type="submit"
                      disabled={isUpdating}
                      className="btn-primary"
                    >
                      {isUpdating ? 'Updating...' : 'Update Settings'}
                    </button>
                  </div>
                </form>
              </div>
            )}

            {/* Payments Settings */}
            {activeTab === 'payments' && (
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-6">Payment Settings</h2>
                
                <form onSubmit={handleSubmitStore(onSubmitStore)} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Currency
                      </label>
                      <select {...registerStore('currency')} className="input-field">
                        <option value="USD">USD - US Dollar</option>
                        <option value="EUR">EUR - Euro</option>
                        <option value="GBP">GBP - British Pound</option>
                        <option value="CAD">CAD - Canadian Dollar</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Tax Rate (%)
                      </label>
                      <input
                        {...registerStore('taxRate', { 
                          required: 'Tax rate is required',
                          min: { value: 0, message: 'Tax rate must be positive' },
                          max: { value: 100, message: 'Tax rate cannot exceed 100%' }
                        })}
                        type="number"
                        step="0.01"
                        className="input-field"
                      />
                      {storeErrors.taxRate && (
                        <p className="mt-1 text-sm text-red-600">{storeErrors.taxRate.message}</p>
                      )}
                    </div>
                  </div>

                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h3 className="text-sm font-medium text-blue-900 mb-2">Payment Methods</h3>
                    <div className="space-y-2">
                      <label className="flex items-center">
                        <input type="checkbox" className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" defaultChecked />
                        <span className="ml-2 text-sm text-blue-700">Credit/Debit Cards (Stripe)</span>
                      </label>
                      <label className="flex items-center">
                        <input type="checkbox" className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" />
                        <span className="ml-2 text-sm text-blue-700">PayPal</span>
                      </label>
                      <label className="flex items-center">
                        <input type="checkbox" className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" />
                        <span className="ml-2 text-sm text-blue-700">Apple Pay</span>
                      </label>
                    </div>
                  </div>

                  <div>
                    <button
                      type="submit"
                      disabled={isUpdating}
                      className="btn-primary"
                    >
                      {isUpdating ? 'Updating...' : 'Update Payment Settings'}
                    </button>
                  </div>
                </form>
              </div>
            )}

            {/* Shipping Settings */}
            {activeTab === 'shipping' && (
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-6">Shipping Settings</h2>
                
                <form onSubmit={handleSubmitStore(onSubmitStore)} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Standard Shipping Rate ($)
                      </label>
                      <input
                        {...registerStore('shippingRate', { 
                          required: 'Shipping rate is required',
                          min: { value: 0, message: 'Shipping rate must be positive' }
                        })}
                        type="number"
                        step="0.01"
                        className="input-field"
                      />
                      {storeErrors.shippingRate && (
                        <p className="mt-1 text-sm text-red-600">{storeErrors.shippingRate.message}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Free Shipping Threshold ($)
                      </label>
                      <input
                        {...registerStore('freeShippingThreshold', { 
                          required: 'Free shipping threshold is required',
                          min: { value: 0, message: 'Threshold must be positive' }
                        })}
                        type="number"
                        step="0.01"
                        className="input-field"
                      />
                      {storeErrors.freeShippingThreshold && (
                        <p className="mt-1 text-sm text-red-600">{storeErrors.freeShippingThreshold.message}</p>
                      )}
                    </div>
                  </div>

                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h3 className="text-sm font-medium text-green-900 mb-2">Shipping Zones</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-green-700">United States</span>
                        <span className="text-sm font-medium text-green-900">Free over $50</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-green-700">Canada</span>
                        <span className="text-sm font-medium text-green-900">$15.00</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-green-700">International</span>
                        <span className="text-sm font-medium text-green-900">$25.00</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <button
                      type="submit"
                      disabled={isUpdating}
                      className="btn-primary"
                    >
                      {isUpdating ? 'Updating...' : 'Update Shipping Settings'}
                    </button>
                  </div>
                </form>
              </div>
            )}

            {/* Notifications Settings */}
            {activeTab === 'notifications' && (
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-6">Notification Settings</h2>
                
                <form onSubmit={handleSubmitNotifications(onSubmitNotifications)} className="space-y-6">
                  <div className="space-y-4">
                    <label className="flex items-center">
                      <input
                        {...registerNotifications('emailNotifications')}
                        type="checkbox"
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      />
                      <div className="ml-3">
                        <span className="text-sm font-medium text-gray-700">Email Notifications</span>
                        <p className="text-sm text-gray-500">Receive email notifications for important events</p>
                      </div>
                    </label>

                    <label className="flex items-center">
                      <input
                        {...registerNotifications('orderNotifications')}
                        type="checkbox"
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      />
                      <div className="ml-3">
                        <span className="text-sm font-medium text-gray-700">Order Notifications</span>
                        <p className="text-sm text-gray-500">Get notified when new orders are placed</p>
                      </div>
                    </label>

                    <label className="flex items-center">
                      <input
                        {...registerNotifications('lowStockNotifications')}
                        type="checkbox"
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      />
                      <div className="ml-3">
                        <span className="text-sm font-medium text-gray-700">Low Stock Notifications</span>
                        <p className="text-sm text-gray-500">Alert when products are running low on stock</p>
                      </div>
                    </label>

                    <label className="flex items-center">
                      <input
                        {...registerNotifications('newUserNotifications')}
                        type="checkbox"
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      />
                      <div className="ml-3">
                        <span className="text-sm font-medium text-gray-700">New User Notifications</span>
                        <p className="text-sm text-gray-500">Get notified when new users register</p>
                      </div>
                    </label>
                  </div>

                  <div>
                    <button
                      type="submit"
                      disabled={isUpdating}
                      className="btn-primary"
                    >
                      {isUpdating ? 'Updating...' : 'Update Notification Settings'}
                    </button>
                  </div>
                </form>
              </div>
            )}

            {/* Security Settings */}
            {activeTab === 'security' && (
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-6">Security Settings</h2>
                
                <div className="space-y-6">
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <h3 className="text-sm font-medium text-yellow-900 mb-2">Two-Factor Authentication</h3>
                    <p className="text-sm text-yellow-700 mb-3">
                      Add an extra layer of security to your admin account.
                    </p>
                    <button className="btn-outline text-yellow-700 border-yellow-300 hover:bg-yellow-100">
                      Enable 2FA
                    </button>
                  </div>

                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <h3 className="text-sm font-medium text-red-900 mb-2">Session Management</h3>
                    <p className="text-sm text-red-700 mb-3">
                      Manage active sessions and force logout from all devices.
                    </p>
                    <button className="btn-outline text-red-700 border-red-300 hover:bg-red-100">
                      Logout All Sessions
                    </button>
                  </div>

                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h3 className="text-sm font-medium text-blue-900 mb-2">API Keys</h3>
                    <p className="text-sm text-blue-700 mb-3">
                      Manage API keys for third-party integrations.
                    </p>
                    <button className="btn-outline text-blue-700 border-blue-300 hover:bg-blue-100">
                      Manage API Keys
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Integrations Settings */}
            {activeTab === 'integrations' && (
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-6">Integrations</h2>
                
                <div className="space-y-6">
                  <div className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-sm font-medium text-gray-900">Google Analytics</h3>
                        <p className="text-sm text-gray-500">Track website traffic and user behavior</p>
                      </div>
                      <button className="btn-outline">Connect</button>
                    </div>
                  </div>

                  <div className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-sm font-medium text-gray-900">Mailchimp</h3>
                        <p className="text-sm text-gray-500">Email marketing and newsletter management</p>
                      </div>
                      <button className="btn-outline">Connect</button>
                    </div>
                  </div>

                  <div className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-sm font-medium text-gray-900">Facebook Pixel</h3>
                        <p className="text-sm text-gray-500">Track conversions and optimize ads</p>
                      </div>
                      <button className="btn-outline">Connect</button>
                    </div>
                  </div>

                  <div className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-sm font-medium text-gray-900">Zapier</h3>
                        <p className="text-sm text-gray-500">Automate workflows with 3000+ apps</p>
                      </div>
                      <button className="btn-outline">Connect</button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
