'use client'

import { useState, useEffect } from 'react'
import {
  UsersIcon,
  CubeIcon,
  ShoppingBagIcon,
  CurrencyDollarIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  TagIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline'
import { formatPrice } from '@/lib/utils'

interface DashboardStats {
  total_users: number
  total_products: number
  total_orders: number
  total_revenue: number
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    total_users: 0,
    total_products: 0,
    total_orders: 0,
    total_revenue: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        // Simulate API call - replace with actual API call
        await new Promise(resolve => setTimeout(resolve, 1000))
        setStats({
          total_users: 1250,
          total_products: 89,
          total_orders: 342,
          total_revenue: 45678.90
        })
      } catch (error) {
        console.error('Failed to fetch dashboard stats:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [])

  const statCards = [
    {
      name: 'Total Users',
      value: stats.total_users.toLocaleString(),
      icon: UsersIcon,
      change: '+12%',
      changeType: 'increase',
      color: 'bg-blue-500'
    },
    {
      name: 'Total Products',
      value: stats.total_products.toLocaleString(),
      icon: CubeIcon,
      change: '+5%',
      changeType: 'increase',
      color: 'bg-green-500'
    },
    {
      name: 'Total Orders',
      value: stats.total_orders.toLocaleString(),
      icon: ShoppingBagIcon,
      change: '+18%',
      changeType: 'increase',
      color: 'bg-purple-500'
    },
    {
      name: 'Total Revenue',
      value: formatPrice(stats.total_revenue),
      icon: CurrencyDollarIcon,
      change: '+23%',
      changeType: 'increase',
      color: 'bg-yellow-500'
    }
  ]

  const recentOrders = [
    { id: 1, customer: 'John Doe', amount: 129.99, status: 'completed', date: '2024-01-15' },
    { id: 2, customer: 'Jane Smith', amount: 89.50, status: 'pending', date: '2024-01-15' },
    { id: 3, customer: 'Bob Johnson', amount: 199.99, status: 'shipped', date: '2024-01-14' },
    { id: 4, customer: 'Alice Brown', amount: 75.25, status: 'completed', date: '2024-01-14' },
    { id: 5, customer: 'Charlie Wilson', amount: 299.99, status: 'processing', date: '2024-01-13' }
  ]

  const topProducts = [
    { id: 1, name: 'Wireless Headphones', sales: 145, revenue: 21675.00 },
    { id: 2, name: 'Smartphone Case', sales: 89, revenue: 2670.00 },
    { id: 3, name: 'Laptop Stand', sales: 67, revenue: 4020.00 },
    { id: 4, name: 'Coffee Mug', sales: 234, revenue: 3510.00 },
    { id: 5, name: 'Desk Lamp', sales: 45, revenue: 2250.00 }
  ]

  if (loading) {
    return (
      <div className="px-4 sm:px-6 lg:px-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-300 rounded w-48 mb-8"></div>
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-white overflow-hidden shadow rounded-lg p-5">
                <div className="h-4 bg-gray-300 rounded mb-4"></div>
                <div className="h-8 bg-gray-300 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="mt-1 text-sm text-gray-500">
          Welcome back! Here's what's happening with your store today.
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        {statCards.map((stat) => {
          const Icon = stat.icon
          return (
            <div key={stat.name} className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className={`${stat.color} p-3 rounded-md`}>
                      <Icon className="h-6 w-6 text-white" />
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        {stat.name}
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {stat.value}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-5 py-3">
                <div className="text-sm">
                  <span className={`font-medium ${
                    stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {stat.changeType === 'increase' ? (
                      <TrendingUpIcon className="inline h-4 w-4 mr-1" />
                    ) : (
                      <TrendingDownIcon className="inline h-4 w-4 mr-1" />
                    )}
                    {stat.change}
                  </span>
                  <span className="text-gray-500"> from last month</span>
                </div>
              </div>
            </div>
          )
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Orders */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Recent Orders
            </h3>
            <div className="flow-root">
              <ul className="-my-5 divide-y divide-gray-200">
                {recentOrders.map((order) => (
                  <li key={order.id} className="py-4">
                    <div className="flex items-center space-x-4">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {order.customer}
                        </p>
                        <p className="text-sm text-gray-500">
                          {order.date}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          order.status === 'completed' ? 'bg-green-100 text-green-800' :
                          order.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                          order.status === 'shipped' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {order.status}
                        </span>
                        <span className="text-sm font-medium text-gray-900">
                          {formatPrice(order.amount)}
                        </span>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
            <div className="mt-6">
              <a
                href="/admin/orders"
                className="w-full flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                View all orders
              </a>
            </div>
          </div>
        </div>

        {/* Top Products */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Top Products
            </h3>
            <div className="flow-root">
              <ul className="-my-5 divide-y divide-gray-200">
                {topProducts.map((product, index) => (
                  <li key={product.id} className="py-4">
                    <div className="flex items-center space-x-4">
                      <div className="flex-shrink-0">
                        <span className="inline-flex items-center justify-center h-8 w-8 rounded-full bg-primary-100 text-primary-800 text-sm font-medium">
                          {index + 1}
                        </span>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {product.name}
                        </p>
                        <p className="text-sm text-gray-500">
                          {product.sales} sales
                        </p>
                      </div>
                      <div className="text-sm font-medium text-gray-900">
                        {formatPrice(product.revenue)}
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
            <div className="mt-6">
              <a
                href="/admin/products"
                className="w-full flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                View all products
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mt-8">
        <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
          Quick Actions
        </h3>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <a
            href="/admin/products/new"
            className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-primary-500 rounded-lg shadow hover:shadow-md transition-shadow"
          >
            <div>
              <span className="rounded-lg inline-flex p-3 bg-primary-50 text-primary-600 ring-4 ring-white">
                <CubeIcon className="h-6 w-6" />
              </span>
            </div>
            <div className="mt-4">
              <h3 className="text-lg font-medium text-gray-900">
                Add Product
              </h3>
              <p className="mt-2 text-sm text-gray-500">
                Add a new product to your inventory
              </p>
            </div>
          </a>

          <a
            href="/admin/categories/new"
            className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-primary-500 rounded-lg shadow hover:shadow-md transition-shadow"
          >
            <div>
              <span className="rounded-lg inline-flex p-3 bg-green-50 text-green-600 ring-4 ring-white">
                <TagIcon className="h-6 w-6" />
              </span>
            </div>
            <div className="mt-4">
              <h3 className="text-lg font-medium text-gray-900">
                Add Category
              </h3>
              <p className="mt-2 text-sm text-gray-500">
                Create a new product category
              </p>
            </div>
          </a>

          <a
            href="/admin/orders"
            className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-primary-500 rounded-lg shadow hover:shadow-md transition-shadow"
          >
            <div>
              <span className="rounded-lg inline-flex p-3 bg-purple-50 text-purple-600 ring-4 ring-white">
                <ShoppingBagIcon className="h-6 w-6" />
              </span>
            </div>
            <div className="mt-4">
              <h3 className="text-lg font-medium text-gray-900">
                Manage Orders
              </h3>
              <p className="mt-2 text-sm text-gray-500">
                View and manage customer orders
              </p>
            </div>
          </a>

          <a
            href="/admin/analytics"
            className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-primary-500 rounded-lg shadow hover:shadow-md transition-shadow"
          >
            <div>
              <span className="rounded-lg inline-flex p-3 bg-yellow-50 text-yellow-600 ring-4 ring-white">
                <ChartBarIcon className="h-6 w-6" />
              </span>
            </div>
            <div className="mt-4">
              <h3 className="text-lg font-medium text-gray-900">
                View Analytics
              </h3>
              <p className="mt-2 text-sm text-gray-500">
                Check your store performance
              </p>
            </div>
          </a>
        </div>
      </div>
    </div>
  )
}
