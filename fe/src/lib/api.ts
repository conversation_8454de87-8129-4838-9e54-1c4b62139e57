import axios from 'axios'
import Cookies from 'js-cookie'
import { 
  User, 
  Product, 
  Category, 
  CartItem, 
  Order, 
  LoginRequest, 
  RegisterRequest, 
  AuthResponse 
} from '@/types'

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

const api = axios.create({
  baseURL: `${API_URL}/api/v1`,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = Cookies.get('access_token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// Handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      Cookies.remove('access_token')
      window.location.href = '/auth/login'
    }
    return Promise.reject(error)
  }
)

// Auth API
export const authAPI = {
  login: async (data: LoginRequest): Promise<AuthResponse> => {
    const response = await api.post('/auth/login', data)
    return response.data
  },
  
  register: async (data: RegisterRequest): Promise<User> => {
    const response = await api.post('/auth/register', data)
    return response.data
  },
}

// User API
export const userAPI = {
  getProfile: async (): Promise<User> => {
    const response = await api.get('/users/me')
    return response.data
  },
  
  updateProfile: async (data: Partial<User>): Promise<User> => {
    const response = await api.put('/users/me', data)
    return response.data
  },
}

// Product API
export const productAPI = {
  getProducts: async (params?: {
    skip?: number
    limit?: number
    search?: string
    category_id?: number
    min_price?: number
    max_price?: number
    is_featured?: boolean
  }): Promise<Product[]> => {
    const response = await api.get('/products/', { params })
    return response.data
  },
  
  getProduct: async (id: number): Promise<Product> => {
    const response = await api.get(`/products/${id}`)
    return response.data
  },
  
  getFeaturedProducts: async (limit = 10): Promise<Product[]> => {
    const response = await api.get('/products/featured', { params: { limit } })
    return response.data
  },
}

// Category API
export const categoryAPI = {
  getCategories: async (): Promise<Category[]> => {
    const response = await api.get('/categories/')
    return response.data
  },
  
  getCategory: async (id: number): Promise<Category> => {
    const response = await api.get(`/categories/${id}`)
    return response.data
  },
}

// Cart API
export const cartAPI = {
  getCartItems: async (): Promise<CartItem[]> => {
    const response = await api.get('/cart/')
    return response.data
  },
  
  addToCart: async (productId: number, quantity: number): Promise<CartItem> => {
    const response = await api.post('/cart/', {
      product_id: productId,
      quantity,
    })
    return response.data
  },
  
  updateCartItem: async (itemId: number, quantity: number): Promise<CartItem> => {
    const response = await api.put(`/cart/${itemId}`, { quantity })
    return response.data
  },
  
  removeFromCart: async (itemId: number): Promise<void> => {
    await api.delete(`/cart/${itemId}`)
  },
  
  clearCart: async (): Promise<void> => {
    await api.delete('/cart/')
  },
}

// Order API
export const orderAPI = {
  getOrders: async (): Promise<Order[]> => {
    const response = await api.get('/orders/')
    return response.data
  },
  
  getOrder: async (id: number): Promise<Order> => {
    const response = await api.get(`/orders/${id}`)
    return response.data
  },
  
  createOrder: async (orderData: {
    items: { product_id: number; quantity: number; price: number }[]
    shipping_address: string
    billing_address?: string
    payment_method: string
    notes?: string
  }): Promise<Order> => {
    const response = await api.post('/orders/', orderData)
    return response.data
  },
}

export default api
