'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'
import { CartItem, Product } from '@/types'
import { cartAPI } from '@/lib/api'
import { useAuth } from '@/lib/auth-context'
import toast from 'react-hot-toast'

interface CartContextType {
  items: CartItem[]
  loading: boolean
  addToCart: (product: Product, quantity?: number) => Promise<void>
  updateQuantity: (itemId: number, quantity: number) => Promise<void>
  removeFromCart: (itemId: number) => Promise<void>
  clearCart: () => Promise<void>
  getTotalItems: () => number
  getTotalPrice: () => number
  refreshCart: () => Promise<void>
}

const CartContext = createContext<CartContextType | undefined>(undefined)

export function CartProvider({ children }: { children: React.ReactNode }) {
  const [items, setItems] = useState<CartItem[]>([])
  const [loading, setLoading] = useState(false)
  const { user } = useAuth()

  useEffect(() => {
    if (user) {
      refreshCart()
    } else {
      setItems([])
    }
  }, [user])

  const refreshCart = async () => {
    if (!user) return
    
    try {
      setLoading(true)
      const cartItems = await cartAPI.getCartItems()
      setItems(cartItems)
    } catch (error) {
      console.error('Failed to fetch cart:', error)
    } finally {
      setLoading(false)
    }
  }

  const addToCart = async (product: Product, quantity = 1) => {
    if (!user) {
      toast.error('Please login to add items to cart')
      return
    }

    try {
      await cartAPI.addToCart(product.id, quantity)
      await refreshCart()
      toast.success(`${product.name} added to cart`)
    } catch (error: any) {
      toast.error(error.response?.data?.detail || 'Failed to add to cart')
    }
  }

  const updateQuantity = async (itemId: number, quantity: number) => {
    try {
      if (quantity <= 0) {
        await removeFromCart(itemId)
        return
      }
      
      await cartAPI.updateCartItem(itemId, quantity)
      await refreshCart()
    } catch (error: any) {
      toast.error(error.response?.data?.detail || 'Failed to update cart')
    }
  }

  const removeFromCart = async (itemId: number) => {
    try {
      await cartAPI.removeFromCart(itemId)
      await refreshCart()
      toast.success('Item removed from cart')
    } catch (error: any) {
      toast.error(error.response?.data?.detail || 'Failed to remove item')
    }
  }

  const clearCart = async () => {
    try {
      await cartAPI.clearCart()
      setItems([])
      toast.success('Cart cleared')
    } catch (error: any) {
      toast.error(error.response?.data?.detail || 'Failed to clear cart')
    }
  }

  const getTotalItems = () => {
    return items.reduce((total, item) => total + item.quantity, 0)
  }

  const getTotalPrice = () => {
    return items.reduce((total, item) => {
      const price = item.product?.discount_price || item.product?.price || 0
      return total + (price * item.quantity)
    }, 0)
  }

  return (
    <CartContext.Provider value={{
      items,
      loading,
      addToCart,
      updateQuantity,
      removeFromCart,
      clearCart,
      getTotalItems,
      getTotalPrice,
      refreshCart,
    }}>
      {children}
    </CartContext.Provider>
  )
}

export function useCart() {
  const context = useContext(CartContext)
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider')
  }
  return context
}
