'use client'

import React, { create<PERSON>ontext, useContext, useState, useEffect } from 'react'
import Cookies from 'js-cookie'
import { User, LoginRequest, RegisterRequest } from '@/types'
import { authAPI, userAPI } from '@/lib/api'
import toast from 'react-hot-toast'

interface AuthContextType {
  user: User | null
  loading: boolean
  login: (data: LoginRequest) => Promise<void>
  register: (data: RegisterRequest) => Promise<void>
  logout: () => void
  updateUser: (data: Partial<User>) => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const token = Cookies.get('access_token')
    if (token) {
      fetchUser()
    } else {
      setLoading(false)
    }
  }, [])

  const fetchUser = async () => {
    try {
      const userData = await userAPI.getProfile()
      setUser(userData)
    } catch (error) {
      console.error('Failed to fetch user:', error)
      Cookies.remove('access_token')
    } finally {
      setLoading(false)
    }
  }

  const login = async (data: LoginRequest) => {
    try {
      const response = await authAPI.login(data)
      Cookies.set('access_token', response.access_token, { expires: 7 })
      await fetchUser()
      toast.success('Login successful!')
    } catch (error: any) {
      toast.error(error.response?.data?.detail || 'Login failed')
      throw error
    }
  }

  const register = async (data: RegisterRequest) => {
    try {
      await authAPI.register(data)
      toast.success('Registration successful! Please login.')
    } catch (error: any) {
      toast.error(error.response?.data?.detail || 'Registration failed')
      throw error
    }
  }

  const logout = () => {
    Cookies.remove('access_token')
    setUser(null)
    toast.success('Logged out successfully')
  }

  const updateUser = async (data: Partial<User>) => {
    try {
      const updatedUser = await userAPI.updateProfile(data)
      setUser(updatedUser)
      toast.success('Profile updated successfully!')
    } catch (error: any) {
      toast.error(error.response?.data?.detail || 'Update failed')
      throw error
    }
  }

  return (
    <AuthContext.Provider value={{
      user,
      loading,
      login,
      register,
      logout,
      updateUser,
    }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
