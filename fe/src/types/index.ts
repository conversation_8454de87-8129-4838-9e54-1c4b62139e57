export interface User {
  id: number
  email: string
  username: string
  first_name?: string
  last_name?: string
  phone?: string
  address?: string
  is_active: boolean
  is_admin: boolean
  created_at: string
  updated_at?: string
}

export interface Category {
  id: number
  name: string
  description?: string
  image_url?: string
  is_active: boolean
  created_at: string
}

export interface Product {
  id: number
  name: string
  description?: string
  price: number
  discount_price?: number
  sku?: string
  stock_quantity: number
  image_url?: string
  images?: string
  category_id?: number
  category?: Category
  is_active: boolean
  is_featured: boolean
  weight?: number
  dimensions?: string
  created_at: string
  updated_at?: string
}

export interface CartItem {
  id: number
  user_id: number
  product_id: number
  product?: Product
  quantity: number
  created_at: string
  updated_at?: string
}

export interface OrderItem {
  product_id: number
  product?: Product
  quantity: number
  price: number
}

export interface Order {
  id: number
  user_id: number
  user?: User
  order_number: string
  status: string
  total_amount: number
  shipping_address: string
  billing_address?: string
  payment_method: string
  payment_status: string
  stripe_payment_intent_id?: string
  notes?: string
  products: Product[]
  created_at: string
  updated_at?: string
}

export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  email: string
  username: string
  password: string
  first_name?: string
  last_name?: string
}

export interface AuthResponse {
  access_token: string
  token_type: string
}
