'use client'

import { useState, useRef, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { MagnifyingGlassIcon, XMarkIcon } from '@heroicons/react/24/outline'
import { productAPI } from '@/lib/api'
import { Product } from '@/types'
import { formatPrice, getImageUrl } from '@/lib/utils'
import Image from 'next/image'
import Link from 'next/link'

interface SearchBarProps {
  placeholder?: string
  className?: string
  showSuggestions?: boolean
}

export default function SearchBar({ 
  placeholder = "Search products...", 
  className = "",
  showSuggestions = true 
}: SearchBarProps) {
  const [query, setQuery] = useState('')
  const [suggestions, setSuggestions] = useState<Product[]>([])
  const [isOpen, setIsOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const router = useRouter()
  const searchRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  useEffect(() => {
    const searchProducts = async () => {
      if (query.length < 2) {
        setSuggestions([])
        setIsOpen(false)
        return
      }

      if (!showSuggestions) return

      setLoading(true)
      try {
        const products = await productAPI.getProducts({ 
          search: query, 
          limit: 5 
        })
        setSuggestions(products)
        setIsOpen(products.length > 0)
      } catch (error) {
        console.error('Search failed:', error)
        setSuggestions([])
        setIsOpen(false)
      } finally {
        setLoading(false)
      }
    }

    const debounceTimer = setTimeout(searchProducts, 300)
    return () => clearTimeout(debounceTimer)
  }, [query, showSuggestions])

  const handleSearch = (searchQuery: string = query) => {
    if (searchQuery.trim()) {
      router.push(`/products?search=${encodeURIComponent(searchQuery.trim())}`)
      setIsOpen(false)
      setQuery('')
      inputRef.current?.blur()
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleSearch()
    } else if (e.key === 'Escape') {
      setIsOpen(false)
      inputRef.current?.blur()
    }
  }

  const clearSearch = () => {
    setQuery('')
    setSuggestions([])
    setIsOpen(false)
    inputRef.current?.focus()
  }

  return (
    <div ref={searchRef} className={`relative ${className}`}>
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onKeyDown={handleKeyDown}
          onFocus={() => query.length >= 2 && suggestions.length > 0 && setIsOpen(true)}
          placeholder={placeholder}
          className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
        />
        
        <MagnifyingGlassIcon className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
        
        {query && (
          <button
            onClick={clearSearch}
            className="absolute right-3 top-2.5 h-5 w-5 text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        )}
        
        {loading && (
          <div className="absolute right-3 top-2.5">
            <div className="animate-spin rounded-full h-5 w-5 border-2 border-gray-300 border-t-primary-600"></div>
          </div>
        )}
      </div>

      {/* Search Suggestions */}
      {isOpen && showSuggestions && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto">
          {suggestions.length > 0 ? (
            <>
              <div className="p-2 border-b border-gray-100">
                <p className="text-xs text-gray-500 uppercase tracking-wide font-medium">
                  Products
                </p>
              </div>
              {suggestions.map((product) => (
                <Link
                  key={product.id}
                  href={`/products/${product.id}`}
                  onClick={() => setIsOpen(false)}
                  className="flex items-center p-3 hover:bg-gray-50 transition-colors"
                >
                  <div className="relative w-10 h-10 flex-shrink-0 mr-3">
                    <Image
                      src={getImageUrl(product.image_url)}
                      alt={product.name}
                      fill
                      className="object-cover rounded"
                    />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {product.name}
                    </p>
                    <p className="text-sm text-gray-500">
                      {product.category?.name}
                    </p>
                  </div>
                  <div className="text-sm font-medium text-gray-900">
                    {formatPrice(product.discount_price || product.price)}
                  </div>
                </Link>
              ))}
              <div className="p-3 border-t border-gray-100">
                <button
                  onClick={() => handleSearch()}
                  className="w-full text-left text-sm text-primary-600 hover:text-primary-700 font-medium"
                >
                  View all results for "{query}"
                </button>
              </div>
            </>
          ) : query.length >= 2 && !loading ? (
            <div className="p-4 text-center">
              <p className="text-sm text-gray-500">No products found</p>
              <button
                onClick={() => handleSearch()}
                className="mt-2 text-sm text-primary-600 hover:text-primary-700 font-medium"
              >
                Search anyway
              </button>
            </div>
          ) : null}
        </div>
      )}
    </div>
  )
}
