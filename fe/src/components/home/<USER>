'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Category } from '@/types'
import { categoryAPI } from '@/lib/api'
import { getImageUrl } from '@/lib/utils'

export default function Categories() {
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const data = await categoryAPI.getCategories()
        setCategories(data.slice(0, 6)) // Show only first 6 categories
      } catch (error) {
        console.error('Failed to fetch categories:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchCategories()
  }, [])

  if (loading) {
    return (
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Shop by Category</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Explore our wide range of product categories
            </p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="bg-gray-300 aspect-square rounded-lg mb-4"></div>
                <div className="h-4 bg-gray-300 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </section>
    )
  }

  // Default categories if none are loaded
  const defaultCategories = [
    { id: 1, name: 'Electronics', image_url: 'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=400' },
    { id: 2, name: 'Fashion', image_url: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400' },
    { id: 3, name: 'Home & Garden', image_url: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400' },
    { id: 4, name: 'Sports', image_url: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400' },
    { id: 5, name: 'Books', image_url: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400' },
    { id: 6, name: 'Beauty', image_url: 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400' },
  ]

  const displayCategories = categories.length > 0 ? categories : defaultCategories

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Shop by Category</h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Explore our wide range of product categories and find exactly what you're looking for
          </p>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 mb-12">
          {displayCategories.map((category) => (
            <Link
              key={category.id}
              href={`/products?category=${category.id}`}
              className="group text-center"
            >
              <div className="relative aspect-square rounded-lg overflow-hidden mb-4 bg-white shadow-md group-hover:shadow-lg transition-shadow duration-300">
                <Image
                  src={getImageUrl(category.image_url)}
                  alt={category.name}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-300"></div>
              </div>
              <h3 className="font-semibold text-gray-900 group-hover:text-primary-600 transition-colors">
                {category.name}
              </h3>
            </Link>
          ))}
        </div>
        
        <div className="text-center">
          <Link href="/categories" className="btn-outline px-8 py-3 text-lg">
            View All Categories
          </Link>
        </div>
      </div>
    </section>
  )
}
