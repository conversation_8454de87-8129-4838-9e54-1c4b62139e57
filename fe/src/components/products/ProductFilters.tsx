'use client'

import { useState } from 'react'
import { Category } from '@/types'
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline'

interface ProductFiltersProps {
  categories: Category[]
  filters: {
    search: string
    category_id?: number
    min_price?: number
    max_price?: number
    is_featured?: boolean
  }
  onFilterChange: (filters: any) => void
}

export default function ProductFilters({ categories, filters, onFilterChange }: ProductFiltersProps) {
  const [expandedSections, setExpandedSections] = useState({
    categories: true,
    price: true,
    features: true,
  })

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const handleSearchChange = (search: string) => {
    onFilterChange({ ...filters, search })
  }

  const handleCategoryChange = (categoryId: number | undefined) => {
    onFilterChange({ ...filters, category_id: categoryId })
  }

  const handlePriceChange = (min_price?: number, max_price?: number) => {
    onFilterChange({ ...filters, min_price, max_price })
  }

  const handleFeaturedChange = (is_featured?: boolean) => {
    onFilterChange({ ...filters, is_featured })
  }

  const clearFilters = () => {
    onFilterChange({
      search: '',
      category_id: undefined,
      min_price: undefined,
      max_price: undefined,
      is_featured: undefined,
    })
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6 sticky top-4">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Filters</h3>
        <button
          onClick={clearFilters}
          className="text-sm text-primary-600 hover:text-primary-700"
        >
          Clear All
        </button>
      </div>

      {/* Search */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Search Products
        </label>
        <input
          type="text"
          value={filters.search}
          onChange={(e) => handleSearchChange(e.target.value)}
          placeholder="Search by name or description..."
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
        />
      </div>

      {/* Categories */}
      <div className="mb-6">
        <button
          onClick={() => toggleSection('categories')}
          className="flex justify-between items-center w-full text-left"
        >
          <span className="text-sm font-medium text-gray-700">Categories</span>
          {expandedSections.categories ? (
            <ChevronUpIcon className="h-4 w-4" />
          ) : (
            <ChevronDownIcon className="h-4 w-4" />
          )}
        </button>
        
        {expandedSections.categories && (
          <div className="mt-3 space-y-2">
            <label className="flex items-center">
              <input
                type="radio"
                name="category"
                checked={!filters.category_id}
                onChange={() => handleCategoryChange(undefined)}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
              />
              <span className="ml-2 text-sm text-gray-600">All Categories</span>
            </label>
            {categories.map((category) => (
              <label key={category.id} className="flex items-center">
                <input
                  type="radio"
                  name="category"
                  checked={filters.category_id === category.id}
                  onChange={() => handleCategoryChange(category.id)}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                />
                <span className="ml-2 text-sm text-gray-600">{category.name}</span>
              </label>
            ))}
          </div>
        )}
      </div>

      {/* Price Range */}
      <div className="mb-6">
        <button
          onClick={() => toggleSection('price')}
          className="flex justify-between items-center w-full text-left"
        >
          <span className="text-sm font-medium text-gray-700">Price Range</span>
          {expandedSections.price ? (
            <ChevronUpIcon className="h-4 w-4" />
          ) : (
            <ChevronDownIcon className="h-4 w-4" />
          )}
        </button>
        
        {expandedSections.price && (
          <div className="mt-3 space-y-3">
            <div className="flex gap-2">
              <input
                type="number"
                placeholder="Min"
                value={filters.min_price || ''}
                onChange={(e) => handlePriceChange(
                  e.target.value ? parseFloat(e.target.value) : undefined,
                  filters.max_price
                )}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
              <input
                type="number"
                placeholder="Max"
                value={filters.max_price || ''}
                onChange={(e) => handlePriceChange(
                  filters.min_price,
                  e.target.value ? parseFloat(e.target.value) : undefined
                )}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            
            {/* Quick price ranges */}
            <div className="space-y-2">
              {[
                { label: 'Under $25', min: 0, max: 25 },
                { label: '$25 - $50', min: 25, max: 50 },
                { label: '$50 - $100', min: 50, max: 100 },
                { label: '$100 - $200', min: 100, max: 200 },
                { label: 'Over $200', min: 200, max: undefined },
              ].map((range) => (
                <button
                  key={range.label}
                  onClick={() => handlePriceChange(range.min, range.max)}
                  className="block w-full text-left text-sm text-gray-600 hover:text-primary-600 py-1"
                >
                  {range.label}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Features */}
      <div className="mb-6">
        <button
          onClick={() => toggleSection('features')}
          className="flex justify-between items-center w-full text-left"
        >
          <span className="text-sm font-medium text-gray-700">Features</span>
          {expandedSections.features ? (
            <ChevronUpIcon className="h-4 w-4" />
          ) : (
            <ChevronDownIcon className="h-4 w-4" />
          )}
        </button>
        
        {expandedSections.features && (
          <div className="mt-3 space-y-2">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={filters.is_featured === true}
                onChange={(e) => handleFeaturedChange(e.target.checked ? true : undefined)}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-600">Featured Products</span>
            </label>
          </div>
        )}
      </div>
    </div>
  )
}
