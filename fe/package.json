{"name": "my-shop-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"next": "14.0.4", "react": "^18", "react-dom": "^18", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5", "tailwindcss": "^3.3.0", "autoprefixer": "^10.0.1", "postcss": "^8", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "axios": "^1.6.2", "js-cookie": "^3.0.5", "@types/js-cookie": "^3.0.6", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "react-hot-toast": "^2.4.1", "lucide-react": "^0.294.0", "@stripe/stripe-js": "^2.2.0", "@stripe/react-stripe-js": "^2.4.0", "clsx": "^2.0.0", "class-variance-authority": "^0.7.0", "tailwind-merge": "^2.2.0"}, "devDependencies": {"eslint": "^8", "eslint-config-next": "14.0.4"}}