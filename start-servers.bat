@echo off
REM My Shop - Windows Batch Script to Start Both Servers
REM This script starts both FastAPI backend and Next.js frontend servers

setlocal enabledelayedexpansion

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        MY SHOP                               ║
echo ║                Development Server Manager                    ║
echo ║                                                              ║
echo ║  Backend (FastAPI):  http://localhost:8000                  ║
echo ║  Frontend (Next.js): http://localhost:3000                  ║
echo ║  API Docs:          http://localhost:8000/docs              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM Check if directories exist
if not exist "be\" (
    echo ❌ Backend directory 'be' not found!
    echo Please run this script from the project root directory.
    pause
    exit /b 1
)

if not exist "fe\" (
    echo ❌ Frontend directory 'fe' not found!
    echo Please run this script from the project root directory.
    pause
    exit /b 1
)

REM Check if main files exist
if not exist "be\main.py" (
    echo ❌ Backend main.py not found!
    pause
    exit /b 1
)

if not exist "fe\package.json" (
    echo ❌ Frontend package.json not found!
    pause
    exit /b 1
)

echo 🔍 Checking prerequisites...
echo ✅ All prerequisites met

REM Create virtual environment if it doesn't exist
if not exist "be\venv\" (
    echo 🔧 Creating Python virtual environment...
    cd be
    python -m venv venv
    if errorlevel 1 (
        echo ❌ Failed to create virtual environment
        pause
        exit /b 1
    )
    cd ..
    echo ✅ Virtual environment created
)

REM Install backend dependencies
echo 📦 Installing backend dependencies...
cd be
if exist "venv\Scripts\pip.exe" (
    venv\Scripts\pip.exe install -r requirements.txt >nul 2>&1
) else (
    pip install -r requirements.txt >nul 2>&1
)
if errorlevel 1 (
    echo ❌ Failed to install backend dependencies
    pause
    exit /b 1
)
cd ..
echo ✅ Backend dependencies installed

REM Install frontend dependencies
echo 📦 Installing frontend dependencies...
cd fe
if not exist "node_modules\" (
    echo Installing Node.js dependencies...
    npm install >nul 2>&1
    if errorlevel 1 (
        echo ❌ Failed to install frontend dependencies
        pause
        exit /b 1
    )
    echo ✅ Frontend dependencies installed
) else (
    echo ✅ Frontend dependencies already installed
)
cd ..

REM Start backend server
echo 🚀 Starting FastAPI backend server...
cd be
if exist "venv\Scripts\python.exe" (
    start "My Shop Backend" /min venv\Scripts\python.exe main.py
) else (
    start "My Shop Backend" /min python main.py
)
cd ..

REM Wait for backend to start
echo ⏳ Waiting for backend to start...
timeout /t 5 /nobreak >nul

REM Check if backend is running
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:8000' -TimeoutSec 1 -UseBasicParsing | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if errorlevel 1 (
    echo ⏳ Backend still starting, waiting a bit more...
    timeout /t 3 /nobreak >nul
)

echo ✅ Backend server started on http://localhost:8000

REM Start frontend server
echo 🚀 Starting Next.js frontend server...
cd fe
start "My Shop Frontend" npm run dev
cd ..

echo.
echo 🎉 Both servers are starting!
echo.
echo 🌐 Frontend: http://localhost:3000
echo 🔧 Backend:  http://localhost:8000
echo 📚 API Docs: http://localhost:8000/docs
echo.
echo 💡 Two new windows have opened for the servers.
echo 💡 Close those windows or press Ctrl+C in them to stop the servers.
echo.
echo Default login credentials:
echo 👤 Admin: <EMAIL> / admin123
echo 👤 User:  <EMAIL> / password123
echo.
pause
