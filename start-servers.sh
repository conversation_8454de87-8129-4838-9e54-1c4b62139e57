#!/bin/bash

# My Shop - Unix/Linux Shell Script to Start Both Servers
# This script starts both FastAPI backend and Next.js frontend servers

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Global variables for process IDs
BACKEND_PID=""
FRONTEND_PID=""

# Function to print colored output
print_colored() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to print banner
print_banner() {
    print_colored $PURPLE "
╔══════════════════════════════════════════════════════════════╗
║                        MY SHOP                               ║
║                Development Server Manager                    ║
║                                                              ║
║  Backend (FastAPI):  http://localhost:8000                  ║
║  Frontend (Next.js): http://localhost:3000                  ║
║  API Docs:          http://localhost:8000/docs              ║
╚══════════════════════════════════════════════════════════════╝
"
}

# Function to check prerequisites
check_prerequisites() {
    print_colored $CYAN "🔍 Checking prerequisites..."
    
    # Check if directories exist
    if [ ! -d "be" ]; then
        print_colored $RED "❌ Backend directory 'be' not found!"
        print_colored $RED "Please run this script from the project root directory."
        exit 1
    fi
    
    if [ ! -d "fe" ]; then
        print_colored $RED "❌ Frontend directory 'fe' not found!"
        print_colored $RED "Please run this script from the project root directory."
        exit 1
    fi
    
    # Check if main files exist
    if [ ! -f "be/main.py" ]; then
        print_colored $RED "❌ Backend main.py not found!"
        exit 1
    fi
    
    if [ ! -f "fe/package.json" ]; then
        print_colored $RED "❌ Frontend package.json not found!"
        exit 1
    fi
    
    # Check if Python is available
    if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
        print_colored $RED "❌ Python is not installed or not in PATH"
        exit 1
    fi
    
    # Check if Node.js is available
    if ! command -v node &> /dev/null; then
        print_colored $RED "❌ Node.js is not installed or not in PATH"
        exit 1
    fi
    
    # Check if npm is available
    if ! command -v npm &> /dev/null; then
        print_colored $RED "❌ npm is not installed or not in PATH"
        exit 1
    fi
    
    print_colored $GREEN "✅ All prerequisites met"
}

# Function to check if ports are available
check_ports() {
    print_colored $CYAN "🔍 Checking port availability..."
    
    # Check port 8000 (backend)
    if lsof -Pi :8000 -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_colored $YELLOW "⚠️  Port 8000 is already in use (Backend)"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    # Check port 3000 (frontend)
    if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_colored $YELLOW "⚠️  Port 3000 is already in use (Frontend)"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    print_colored $GREEN "✅ Ports are available"
}

# Function to install backend dependencies
install_backend_dependencies() {
    print_colored $CYAN "📦 Checking backend dependencies..."
    
    cd be
    
    # Create virtual environment if it doesn't exist
    if [ ! -d "venv" ]; then
        print_colored $YELLOW "🔧 Creating Python virtual environment..."
        if command -v python3 &> /dev/null; then
            python3 -m venv venv
        else
            python -m venv venv
        fi
        
        if [ $? -ne 0 ]; then
            print_colored $RED "❌ Failed to create virtual environment"
            exit 1
        fi
        print_colored $GREEN "✅ Virtual environment created"
    fi
    
    # Activate virtual environment and install dependencies
    source venv/bin/activate
    
    print_colored $CYAN "📦 Installing Python dependencies..."
    pip install -r requirements.txt > /dev/null 2>&1
    
    if [ $? -ne 0 ]; then
        print_colored $RED "❌ Failed to install backend dependencies"
        exit 1
    fi
    
    print_colored $GREEN "✅ Backend dependencies installed"
    cd ..
}

# Function to install frontend dependencies
install_frontend_dependencies() {
    print_colored $CYAN "📦 Checking frontend dependencies..."
    
    cd fe
    
    if [ ! -d "node_modules" ]; then
        print_colored $CYAN "📦 Installing Node.js dependencies..."
        npm install > /dev/null 2>&1
        
        if [ $? -ne 0 ]; then
            print_colored $RED "❌ Failed to install frontend dependencies"
            exit 1
        fi
        print_colored $GREEN "✅ Frontend dependencies installed"
    else
        print_colored $GREEN "✅ Frontend dependencies already installed"
    fi
    
    cd ..
}

# Function to start backend server
start_backend() {
    print_colored $CYAN "🚀 Starting FastAPI backend server..."
    
    cd be
    
    # Start backend in background
    if [ -f "venv/bin/python" ]; then
        nohup venv/bin/python main.py > backend.log 2>&1 &
    else
        if command -v python3 &> /dev/null; then
            nohup python3 main.py > backend.log 2>&1 &
        else
            nohup python main.py > backend.log 2>&1 &
        fi
    fi
    
    BACKEND_PID=$!
    cd ..
    
    # Wait for backend to start
    print_colored $CYAN "⏳ Waiting for backend to be ready..."
    for i in {1..30}; do
        if curl -s http://localhost:8000 > /dev/null 2>&1; then
            print_colored $GREEN "✅ Backend server started successfully on http://localhost:8000"
            print_colored $BLUE "📚 API Documentation: http://localhost:8000/docs"
            return 0
        fi
        sleep 1
    done
    
    print_colored $RED "❌ Backend server failed to start within 30 seconds"
    return 1
}

# Function to start frontend server
start_frontend() {
    print_colored $CYAN "🚀 Starting Next.js frontend server..."
    
    cd fe
    
    # Start frontend in background
    nohup npm run dev > frontend.log 2>&1 &
    FRONTEND_PID=$!
    cd ..
    
    # Wait a moment for frontend to start
    sleep 3
    
    # Check if process is still running
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        print_colored $GREEN "✅ Frontend server started successfully on http://localhost:3000"
        return 0
    else
        print_colored $RED "❌ Frontend server failed to start"
        return 1
    fi
}

# Function to cleanup processes
cleanup() {
    print_colored $YELLOW "\n🛑 Shutting down servers..."
    
    if [ ! -z "$FRONTEND_PID" ]; then
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            kill -TERM $FRONTEND_PID 2>/dev/null
            wait $FRONTEND_PID 2>/dev/null
            print_colored $GREEN "✅ Frontend server stopped"
        fi
    fi
    
    if [ ! -z "$BACKEND_PID" ]; then
        if kill -0 $BACKEND_PID 2>/dev/null; then
            kill -TERM $BACKEND_PID 2>/dev/null
            wait $BACKEND_PID 2>/dev/null
            print_colored $GREEN "✅ Backend server stopped"
        fi
    fi
    
    # Clean up log files
    [ -f "be/backend.log" ] && rm -f "be/backend.log"
    [ -f "fe/frontend.log" ] && rm -f "fe/frontend.log"
    
    print_colored $PURPLE "👋 Goodbye!"
    exit 0
}

# Function to monitor processes
monitor_processes() {
    while true; do
        sleep 5
        
        # Check if backend process is still running
        if [ ! -z "$BACKEND_PID" ] && ! kill -0 $BACKEND_PID 2>/dev/null; then
            print_colored $RED "❌ Backend process died unexpectedly"
            cleanup
        fi
        
        # Check if frontend process is still running
        if [ ! -z "$FRONTEND_PID" ] && ! kill -0 $FRONTEND_PID 2>/dev/null; then
            print_colored $RED "❌ Frontend process died unexpectedly"
            cleanup
        fi
    done
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Main execution
main() {
    print_banner
    
    check_prerequisites
    check_ports
    install_backend_dependencies
    install_frontend_dependencies
    
    if ! start_backend; then
        cleanup
        exit 1
    fi
    
    if ! start_frontend; then
        cleanup
        exit 1
    fi
    
    # Success message
    print_colored $GREEN "\n🎉 Both servers are running successfully!"
    print_colored $BLUE "🌐 Frontend: http://localhost:3000"
    print_colored $BLUE "🔧 Backend:  http://localhost:8000"
    print_colored $BLUE "📚 API Docs: http://localhost:8000/docs"
    print_colored $YELLOW "\n💡 Press Ctrl+C to stop both servers"
    
    echo
    print_colored $CYAN "Default login credentials:"
    print_colored $CYAN "👤 Admin: <EMAIL> / admin123"
    print_colored $CYAN "👤 User:  <EMAIL> / password123"
    echo
    
    # Monitor processes
    monitor_processes
}

# Run main function
main
