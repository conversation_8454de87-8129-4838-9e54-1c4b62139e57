# My Shop - Complete E-commerce Website

A modern, full-stack e-commerce website built with Next.js (frontend) and FastAPI (backend), featuring a comprehensive product catalog, user authentication, shopping cart, order management, and admin panel.

## 🚀 Features

### Customer Features
- **Product Catalog**: Browse products with search, filtering, and categorization
- **User Authentication**: Secure login/register system with JWT tokens
- **Shopping Cart**: Add, update, and remove items from cart
- **Checkout Process**: Complete order placement with address and payment details
- **Order History**: View past orders and track order status
- **User Profile**: Manage personal information and preferences
- **Responsive Design**: Optimized for desktop and mobile devices

### Admin Features
- **Product Management**: Create, update, and manage product inventory
- **Category Management**: Organize products into categories
- **Order Management**: View and update order statuses
- **User Management**: View and manage customer accounts
- **Analytics Dashboard**: Sales analytics and reporting
- **Inventory Tracking**: Monitor stock levels and product availability

### Technical Features
- **Modern UI/UX**: Built with Tailwind CSS and Headless UI
- **API Documentation**: Auto-generated OpenAPI/Swagger documentation
- **Database Integration**: MariaDB with SQLAlchemy ORM
- **Security**: JWT authentication, password hashing, input validation
- **Error Handling**: Comprehensive error handling and user feedback
- **Performance**: Optimized queries and caching strategies

## 🛠 Technology Stack

### Frontend (fe/)
- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Headless UI, Heroicons
- **State Management**: React Context API
- **HTTP Client**: Axios
- **Form Handling**: React Hook Form with Zod validation
- **Notifications**: React Hot Toast

### Backend (be/)
- **Framework**: FastAPI
- **Language**: Python 3.8+
- **Database**: MariaDB
- **ORM**: SQLAlchemy
- **Authentication**: JWT with python-jose
- **Password Hashing**: bcrypt
- **API Documentation**: OpenAPI/Swagger
- **Validation**: Pydantic

## 📋 Prerequisites

- **Node.js** 18+ and npm/yarn
- **Python** 3.8+
- **MariaDB** server running on localhost:3308
- **Git** for version control

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone <repository-url>
cd my-shop
```

### 2. Database Setup
Make sure MariaDB is running with the following configuration:
- Host: localhost
- Port: 3308
- Database: mydatabase
- Username: root
- Password: 123456

Create the database if it doesn't exist:
```sql
CREATE DATABASE mydatabase;
```

### 3. Backend Setup
```bash
cd be

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Create sample data
python create_sample_data.py

# Start the backend server
python main.py
```

The backend will be available at: http://localhost:8000
API documentation: http://localhost:8000/docs

### 4. Frontend Setup
```bash
cd fe

# Install dependencies
npm install

# Start the development server
npm run dev
```

The frontend will be available at: http://localhost:3000

## 👥 Default Users

After running the sample data script, you can use these accounts:

### Admin Account
- **Email**: <EMAIL>
- **Password**: admin123
- **Access**: Full admin panel access

### Test User Account
- **Email**: <EMAIL>
- **Password**: password123
- **Access**: Customer features

## 📁 Project Structure

```
my-shop/
├── fe/                          # Frontend (Next.js)
│   ├── src/
│   │   ├── app/                 # App Router pages
│   │   │   ├── auth/           # Authentication pages
│   │   │   ├── products/       # Product pages
│   │   │   ├── cart/           # Shopping cart
│   │   │   └── ...
│   │   ├── components/         # Reusable components
│   │   │   ├── layout/         # Layout components
│   │   │   ├── home/           # Home page components
│   │   │   ├── products/       # Product components
│   │   │   └── ...
│   │   ├── lib/                # Utilities and API client
│   │   └── types/              # TypeScript definitions
│   ├── public/                 # Static assets
│   └── package.json
├── be/                         # Backend (FastAPI)
│   ├── app/
│   │   ├── api/                # API endpoints
│   │   │   └── api_v1/
│   │   │       └── endpoints/  # Route handlers
│   │   ├── core/               # Core functionality
│   │   ├── models/             # Database models
│   │   ├── schemas/            # Pydantic schemas
│   │   └── services/           # Business logic
│   ├── main.py                 # FastAPI application
│   ├── requirements.txt        # Python dependencies
│   └── create_sample_data.py   # Sample data script
└── README.md
```

## 🔧 Configuration

### Backend Configuration
Edit `be/app/core/config.py` to modify:
- Database connection settings
- JWT secret key and expiration
- CORS origins
- Stripe API keys (for payment processing)

### Frontend Configuration
Edit `fe/next.config.js` to modify:
- API URL
- Image domains
- Environment variables

## 🌟 Key Features Explained

### Authentication System
- JWT-based authentication with secure token storage
- Password hashing using bcrypt
- Protected routes and API endpoints
- Role-based access control (admin/user)

### Product Management
- Full CRUD operations for products and categories
- Image upload and management
- Stock quantity tracking
- Featured products and discounts
- Search and filtering capabilities

### Shopping Cart
- Persistent cart storage
- Real-time quantity updates
- Price calculations with discounts
- Cart synchronization across sessions

### Order Processing
- Complete checkout flow
- Order status tracking
- Email notifications (configurable)
- Payment integration ready (Stripe)

### Admin Dashboard
- Sales analytics and reporting
- Inventory management
- Order fulfillment tracking
- User management tools

## 🚀 Deployment

### Backend Deployment
1. Set up a production database
2. Configure environment variables
3. Install dependencies: `pip install -r requirements.txt`
4. Run with a production WSGI server like Gunicorn:
   ```bash
   gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker
   ```

### Frontend Deployment
1. Build the application: `npm run build`
2. Deploy to platforms like Vercel, Netlify, or your preferred hosting service
3. Configure environment variables for production API URL

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Commit your changes: `git commit -am 'Add new feature'`
4. Push to the branch: `git push origin feature/new-feature`
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

If you encounter any issues or have questions:
1. Check the API documentation at http://localhost:8000/docs
2. Review the console logs for error messages
3. Ensure all dependencies are properly installed
4. Verify database connection settings

## 🔮 Future Enhancements

- Payment gateway integration (Stripe, PayPal)
- Email notifications for orders
- Product reviews and ratings
- Wishlist functionality
- Advanced search with Elasticsearch
- Mobile app development
- Multi-language support
- Social media integration
