# My Shop Backend - FastAPI E-commerce API

A robust and scalable e-commerce backend API built with FastAPI, featuring user authentication, product management, order processing, and admin functionality.

## 🚀 Features

- **RESTful API**: Clean and intuitive API design
- **Authentication**: JWT-based authentication system
- **Database**: MariaDB integration with SQLAlchemy ORM
- **Documentation**: Auto-generated OpenAPI/Swagger documentation
- **Validation**: Request/response validation with Pydantic
- **Security**: Password hashing, CORS protection, input sanitization
- **Admin Panel**: Complete admin functionality for managing the store

## 📋 Prerequisites

- Python 3.8+
- MariaDB server
- pip (Python package manager)

## 🛠 Installation

1. **Create virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure database**:
   - Ensure MariaDB is running on localhost:3308
   - Create database: `CREATE DATABASE mydatabase;`
   - Update connection settings in `app/core/config.py` if needed

4. **Create sample data**:
   ```bash
   python create_sample_data.py
   ```

5. **Start the server**:
   ```bash
   python main.py
   ```

The API will be available at: http://localhost:8000
Interactive documentation: http://localhost:8000/docs

## 📚 API Documentation

### Authentication Endpoints
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/register` - User registration

### User Endpoints
- `GET /api/v1/users/me` - Get current user profile
- `PUT /api/v1/users/me` - Update user profile

### Product Endpoints
- `GET /api/v1/products/` - List products with filtering
- `GET /api/v1/products/{id}` - Get product details
- `GET /api/v1/products/featured` - Get featured products

### Category Endpoints
- `GET /api/v1/categories/` - List categories
- `GET /api/v1/categories/{id}` - Get category details

### Cart Endpoints
- `GET /api/v1/cart/` - Get user's cart items
- `POST /api/v1/cart/` - Add item to cart
- `PUT /api/v1/cart/{item_id}` - Update cart item
- `DELETE /api/v1/cart/{item_id}` - Remove item from cart

### Order Endpoints
- `GET /api/v1/orders/` - Get user's orders
- `POST /api/v1/orders/` - Create new order
- `GET /api/v1/orders/{id}` - Get order details

### Admin Endpoints
- `POST /api/v1/admin/products` - Create product
- `PUT /api/v1/admin/products/{id}` - Update product
- `DELETE /api/v1/admin/products/{id}` - Delete product
- `GET /api/v1/admin/orders` - Get all orders
- `PUT /api/v1/admin/orders/{id}` - Update order status
- `GET /api/v1/admin/analytics/dashboard` - Get dashboard analytics

## 🗄 Database Schema

### Users Table
- `id` - Primary key
- `email` - Unique email address
- `username` - Unique username
- `hashed_password` - Bcrypt hashed password
- `first_name`, `last_name` - User names
- `phone`, `address` - Contact information
- `is_active` - Account status
- `is_admin` - Admin privileges
- `created_at`, `updated_at` - Timestamps

### Categories Table
- `id` - Primary key
- `name` - Category name
- `description` - Category description
- `image_url` - Category image
- `is_active` - Category status
- `created_at` - Creation timestamp

### Products Table
- `id` - Primary key
- `name` - Product name
- `description` - Product description
- `price` - Regular price
- `discount_price` - Sale price
- `sku` - Stock keeping unit
- `stock_quantity` - Available quantity
- `image_url` - Main product image
- `images` - Additional images (JSON)
- `category_id` - Foreign key to categories
- `is_active` - Product status
- `is_featured` - Featured product flag
- `weight`, `dimensions` - Physical properties
- `created_at`, `updated_at` - Timestamps

### Orders Table
- `id` - Primary key
- `user_id` - Foreign key to users
- `order_number` - Unique order identifier
- `status` - Order status (pending, confirmed, shipped, delivered, cancelled)
- `total_amount` - Order total
- `shipping_address`, `billing_address` - Addresses
- `payment_method` - Payment method used
- `payment_status` - Payment status
- `stripe_payment_intent_id` - Stripe payment ID
- `notes` - Order notes
- `created_at`, `updated_at` - Timestamps

### Cart Items Table
- `id` - Primary key
- `user_id` - Foreign key to users
- `product_id` - Foreign key to products
- `quantity` - Item quantity
- `created_at`, `updated_at` - Timestamps

### Order Items Table (Association)
- `order_id` - Foreign key to orders
- `product_id` - Foreign key to products
- `quantity` - Item quantity
- `price` - Price at time of order

## 🔧 Configuration

Edit `app/core/config.py` to configure:

```python
class Settings(BaseSettings):
    PROJECT_NAME: str = "My Shop API"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    
    # Database
    DATABASE_URL: str = "mysql+pymysql://root:123456@localhost:3308/mydatabase"
    
    # Security
    SECRET_KEY: str = "your-secret-key-change-this-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # CORS
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = [
        "http://localhost:3000",
        "http://localhost:3001",
    ]
    
    # Stripe (for payment processing)
    STRIPE_SECRET_KEY: str = "sk_test_your_stripe_secret_key"
    STRIPE_PUBLISHABLE_KEY: str = "pk_test_your_stripe_publishable_key"
```

## 🔐 Authentication

The API uses JWT (JSON Web Tokens) for authentication:

1. **Login**: Send credentials to `/api/v1/auth/login`
2. **Receive Token**: Get access token in response
3. **Use Token**: Include in Authorization header: `Bearer <token>`
4. **Token Expiry**: Tokens expire after 30 minutes (configurable)

Example login request:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

Example response:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer"
}
```

## 🛡 Security Features

- **Password Hashing**: Bcrypt with salt
- **JWT Tokens**: Secure token-based authentication
- **CORS Protection**: Configurable CORS origins
- **Input Validation**: Pydantic model validation
- **SQL Injection Protection**: SQLAlchemy ORM
- **Rate Limiting**: Can be added with slowapi
- **HTTPS Ready**: Production-ready security headers

## 📊 Sample Data

The `create_sample_data.py` script creates:
- 2 users (1 admin, 1 regular)
- 6 product categories
- 15+ sample products
- Proper relationships and realistic data

Default accounts:
- **Admin**: <EMAIL> / admin123
- **User**: <EMAIL> / password123

## 🚀 Deployment

### Development
```bash
python main.py
```

### Production
```bash
# Install production dependencies
pip install gunicorn

# Run with Gunicorn
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

### Docker (Optional)
```dockerfile
FROM python:3.9

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

CMD ["gunicorn", "main:app", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8000"]
```

## 🧪 Testing

Run tests with pytest:
```bash
pip install pytest pytest-asyncio httpx
pytest
```

## 📝 API Response Format

### Success Response
```json
{
  "id": 1,
  "name": "Product Name",
  "price": 99.99,
  "created_at": "2023-01-01T00:00:00Z"
}
```

### Error Response
```json
{
  "detail": "Error message description"
}
```

### Validation Error
```json
{
  "detail": [
    {
      "loc": ["body", "email"],
      "msg": "field required",
      "type": "value_error.missing"
    }
  ]
}
```

## 🔍 Monitoring and Logging

- **Logging**: Python logging module
- **Database Queries**: SQLAlchemy echo mode for development
- **Error Tracking**: Can integrate with Sentry
- **Health Checks**: Add `/health` endpoint for monitoring

## 🤝 Contributing

1. Follow PEP 8 style guidelines
2. Add type hints to all functions
3. Write docstrings for public methods
4. Add tests for new features
5. Update API documentation

## 📞 Support

For issues and questions:
1. Check the interactive docs at `/docs`
2. Review server logs for errors
3. Verify database connectivity
4. Ensure all environment variables are set
