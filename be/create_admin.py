#!/usr/bin/env python3
"""
Script to create admin user
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import sessionmaker
from app.core.database import engine
from app.models.models import User
from app.core.security import get_password_hash

# Create session
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
db = SessionLocal()

def create_admin():
    print("Creating admin user...")
    
    # Check if admin already exists
    existing_admin = db.query(User).filter(User.email == "<EMAIL>").first()
    
    if existing_admin:
        print("Admin user already exists, updating password...")
        existing_admin.hashed_password = get_password_hash("admin")
        db.commit()
        print("✅ Admin password updated!")
    else:
        # Create new admin user
        admin_user = User(
            email="<EMAIL>",
            username="admin",
            hashed_password=get_password_hash("admin"),
            first_name="Admin",
            last_name="User",
            is_active=True,
            is_admin=True
        )
        db.add(admin_user)
        db.commit()
        print("✅ Admin user created!")
    
    print("Admin login:")
    print("Email: <EMAIL>")
    print("Password: admin")

if __name__ == "__main__":
    try:
        create_admin()
    except Exception as e:
        print(f"Error: {e}")
        db.rollback()
    finally:
        db.close()
