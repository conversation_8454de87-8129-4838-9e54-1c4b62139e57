from typing import List
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.models.models import Category
from app.schemas.product import Category as CategorySchema

router = APIRouter()

@router.get("/", response_model=List[CategorySchema])
def read_categories(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    categories = db.query(Category).filter(
        Category.is_active == True
    ).offset(skip).limit(limit).all()
    return categories

@router.get("/{category_id}", response_model=CategorySchema)
def read_category(
    category_id: int,
    db: Session = Depends(get_db)
):
    category = db.query(Category).filter(
        Category.id == category_id,
        Category.is_active == True
    ).first()
    if not category:
        raise HTTPException(status_code=404, detail="Category not found")
    return category
