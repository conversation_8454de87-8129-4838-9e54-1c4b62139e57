from typing import Optional, List
from pydantic import BaseModel
from datetime import datetime
from .product import Product
from .user import User

class OrderItemBase(BaseModel):
    product_id: int
    quantity: int
    price: float

class OrderItemCreate(OrderItemBase):
    pass

class OrderItem(OrderItemBase):
    product: Optional[Product] = None

    class Config:
        from_attributes = True

class OrderBase(BaseModel):
    shipping_address: str
    billing_address: Optional[str] = None
    payment_method: str
    notes: Optional[str] = None

class OrderCreate(OrderBase):
    items: List[OrderItemCreate]

class OrderUpdate(BaseModel):
    status: Optional[str] = None
    payment_status: Optional[str] = None
    shipping_address: Optional[str] = None
    billing_address: Optional[str] = None
    notes: Optional[str] = None

class Order(OrderBase):
    id: int
    user_id: int
    order_number: str
    status: str
    total_amount: float
    payment_status: str
    stripe_payment_intent_id: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    user: Optional[User] = None
    products: List[Product] = []

    class Config:
        from_attributes = True
