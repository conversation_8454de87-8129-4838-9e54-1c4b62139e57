#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to create sample data for the e-commerce application
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import sessionmaker
from app.core.database import engine
from app.models.models import User, Category, Product
from app.core.security import get_password_hash

# Create session
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
db = SessionLocal()

def create_sample_data():
    print("Creating sample data...")

    # Create admin user
    admin_user = User(
        email="<EMAIL>",
        username="admin",
        hashed_password=get_password_hash("admin"),
        first_name="Admin",
        last_name="User",
        is_active=True,
        is_admin=True
    )
    db.add(admin_user)

    # Create regular user
    regular_user = User(
        email="<EMAIL>",
        username="testuser",
        hashed_password=get_password_hash("password123"),
        first_name="<PERSON>",
        last_name="<PERSON><PERSON>",
        is_active=True,
        is_admin=False
    )
    db.add(regular_user)

    # Create categories
    categories_data = [
        {
            "name": "Electronics",
            "description": "Latest electronic devices and gadgets",
            "image_url": "https://images.unsplash.com/photo-1498049794561-7780e7231661?w=400"
        },
        {
            "name": "Fashion",
            "description": "Trendy clothing and accessories",
            "image_url": "https://images.unsplash.com/photo-1445205170230-053b83016050?w=400"
        },
        {
            "name": "Home & Garden",
            "description": "Everything for your home and garden",
            "image_url": "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400"
        },
        {
            "name": "Sports & Outdoors",
            "description": "Sports equipment and outdoor gear",
            "image_url": "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400"
        },
        {
            "name": "Books",
            "description": "Books for all ages and interests",
            "image_url": "https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400"
        },
        {
            "name": "Beauty & Health",
            "description": "Beauty products and health supplements",
            "image_url": "https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400"
        }
    ]

    categories = []
    for cat_data in categories_data:
        category = Category(**cat_data)
        db.add(category)
        categories.append(category)

    db.commit()  # Commit to get category IDs

    # Create products
    products_data = [
        # Electronics
        {
            "name": "Wireless Bluetooth Headphones",
            "description": "High-quality wireless headphones with noise cancellation and 30-hour battery life.",
            "price": 199.99,
            "discount_price": 149.99,
            "sku": "WBH-001",
            "stock_quantity": 50,
            "image_url": "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500",
            "category_id": categories[0].id,
            "is_featured": True
        },
        {
            "name": "Smartphone 128GB",
            "description": "Latest smartphone with advanced camera system and fast processor.",
            "price": 799.99,
            "discount_price": 699.99,
            "sku": "SP-128",
            "stock_quantity": 25,
            "image_url": "https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=500",
            "category_id": categories[0].id,
            "is_featured": True
        },
        {
            "name": "Laptop 15.6 inch",
            "description": "Powerful laptop for work and gaming with 16GB RAM and SSD storage.",
            "price": 1299.99,
            "sku": "LP-156",
            "stock_quantity": 15,
            "image_url": "https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=500",
            "category_id": categories[0].id,
            "is_featured": True
        },

        # Fashion
        {
            "name": "Classic Denim Jacket",
            "description": "Timeless denim jacket perfect for any casual outfit.",
            "price": 89.99,
            "discount_price": 69.99,
            "sku": "DJ-001",
            "stock_quantity": 40,
            "image_url": "https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?w=500",
            "category_id": categories[1].id,
            "is_featured": True
        },
        {
            "name": "Running Sneakers",
            "description": "Comfortable running shoes with excellent cushioning and support.",
            "price": 129.99,
            "discount_price": 99.99,
            "sku": "RS-001",
            "stock_quantity": 60,
            "image_url": "https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=500",
            "category_id": categories[1].id,
            "is_featured": True
        },
        {
            "name": "Cotton T-Shirt",
            "description": "Soft and comfortable 100% cotton t-shirt in various colors.",
            "price": 24.99,
            "sku": "CT-001",
            "stock_quantity": 100,
            "image_url": "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500",
            "category_id": categories[1].id
        },

        # Home & Garden
        {
            "name": "Coffee Maker",
            "description": "Programmable coffee maker with thermal carafe and auto-shutoff.",
            "price": 149.99,
            "discount_price": 119.99,
            "sku": "CM-001",
            "stock_quantity": 30,
            "image_url": "https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=500",
            "category_id": categories[2].id,
            "is_featured": True
        },
        {
            "name": "Indoor Plant Set",
            "description": "Set of 3 beautiful indoor plants perfect for home decoration.",
            "price": 79.99,
            "sku": "IPS-001",
            "stock_quantity": 20,
            "image_url": "https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=500",
            "category_id": categories[2].id
        },

        # Sports & Outdoors
        {
            "name": "Yoga Mat",
            "description": "Non-slip yoga mat with excellent grip and cushioning.",
            "price": 39.99,
            "discount_price": 29.99,
            "sku": "YM-001",
            "stock_quantity": 75,
            "image_url": "https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=500",
            "category_id": categories[3].id,
            "is_featured": True
        },
        {
            "name": "Water Bottle",
            "description": "Insulated stainless steel water bottle keeps drinks cold for 24 hours.",
            "price": 34.99,
            "sku": "WB-001",
            "stock_quantity": 80,
            "image_url": "https://images.unsplash.com/photo-1602143407151-7111542de6e8?w=500",
            "category_id": categories[3].id
        },

        # Books
        {
            "name": "Programming Guide",
            "description": "Comprehensive guide to modern programming languages and techniques.",
            "price": 49.99,
            "discount_price": 39.99,
            "sku": "PG-001",
            "stock_quantity": 35,
            "image_url": "https://images.unsplash.com/photo-1532012197267-da84d127e765?w=500",
            "category_id": categories[4].id
        },
        {
            "name": "Fiction Novel",
            "description": "Bestselling fiction novel with captivating storyline.",
            "price": 19.99,
            "sku": "FN-001",
            "stock_quantity": 50,
            "image_url": "https://images.unsplash.com/photo-**********-fa07a98d237f?w=500",
            "category_id": categories[4].id
        },

        # Beauty & Health
        {
            "name": "Skincare Set",
            "description": "Complete skincare routine with cleanser, toner, and moisturizer.",
            "price": 89.99,
            "discount_price": 69.99,
            "sku": "SS-001",
            "stock_quantity": 45,
            "image_url": "https://images.unsplash.com/photo-**********-0d85b1a4d571?w=500",
            "category_id": categories[5].id,
            "is_featured": True
        },
        {
            "name": "Vitamin Supplements",
            "description": "Daily multivitamin supplements for optimal health.",
            "price": 29.99,
            "sku": "VS-001",
            "stock_quantity": 100,
            "image_url": "https://images.unsplash.com/photo-**********-5c350d0d3c56?w=500",
            "category_id": categories[5].id
        }
    ]

    for product_data in products_data:
        product = Product(**product_data)
        db.add(product)

    db.commit()
    print("Sample data created successfully!")
    print(f"Created {len(categories_data)} categories and {len(products_data)} products")
    print("Admin user: <EMAIL> / admin")
    print("Test user: <EMAIL> / password123")

if __name__ == "__main__":
    try:
        create_sample_data()
    except Exception as e:
        print(f"Error creating sample data: {e}")
        db.rollback()
    finally:
        db.close()
