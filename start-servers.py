#!/usr/bin/env python3
"""
My Shop - Development Server Startup Script
Starts both FastAPI backend and Next.js frontend servers simultaneously
"""

import os
import sys
import subprocess
import signal
import time
import threading
import platform
from pathlib import Path

class Colors:
    """ANSI color codes for terminal output"""
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

class ServerManager:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.is_windows = platform.system() == "Windows"
        self.project_root = Path(__file__).parent.absolute()
        self.backend_dir = self.project_root / "be"
        self.frontend_dir = self.project_root / "fe"
        
    def print_colored(self, message, color=Colors.ENDC):
        """Print colored message to terminal"""
        print(f"{color}{message}{Colors.ENDC}")
        
    def print_banner(self):
        """Print startup banner"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                        MY SHOP                               ║
║                Development Server Manager                    ║
║                                                              ║
║  Backend (FastAPI):  http://localhost:8000                  ║
║  Frontend (Next.js): http://localhost:3000                  ║
║  API Docs:          http://localhost:8000/docs              ║
╚══════════════════════════════════════════════════════════════╝
        """
        self.print_colored(banner, Colors.HEADER)
        
    def check_prerequisites(self):
        """Check if required directories and files exist"""
        self.print_colored("🔍 Checking prerequisites...", Colors.OKCYAN)
        
        # Check directories
        if not self.backend_dir.exists():
            self.print_colored(f"❌ Backend directory not found: {self.backend_dir}", Colors.FAIL)
            return False
            
        if not self.frontend_dir.exists():
            self.print_colored(f"❌ Frontend directory not found: {self.frontend_dir}", Colors.FAIL)
            return False
            
        # Check backend files
        if not (self.backend_dir / "main.py").exists():
            self.print_colored("❌ Backend main.py not found", Colors.FAIL)
            return False
            
        if not (self.backend_dir / "requirements.txt").exists():
            self.print_colored("❌ Backend requirements.txt not found", Colors.FAIL)
            return False
            
        # Check frontend files
        if not (self.frontend_dir / "package.json").exists():
            self.print_colored("❌ Frontend package.json not found", Colors.FAIL)
            return False
            
        self.print_colored("✅ All prerequisites met", Colors.OKGREEN)
        return True
        
    def check_ports(self):
        """Check if required ports are available"""
        import socket
        
        def is_port_in_use(port):
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                return s.connect_ex(('localhost', port)) == 0
                
        self.print_colored("🔍 Checking port availability...", Colors.OKCYAN)
        
        if is_port_in_use(8000):
            self.print_colored("⚠️  Port 8000 is already in use (Backend)", Colors.WARNING)
            response = input("Continue anyway? (y/N): ")
            if response.lower() != 'y':
                return False
                
        if is_port_in_use(3000):
            self.print_colored("⚠️  Port 3000 is already in use (Frontend)", Colors.WARNING)
            response = input("Continue anyway? (y/N): ")
            if response.lower() != 'y':
                return False
                
        self.print_colored("✅ Ports are available", Colors.OKGREEN)
        return True
        
    def install_backend_dependencies(self):
        """Install backend dependencies if needed"""
        self.print_colored("📦 Checking backend dependencies...", Colors.OKCYAN)
        
        # Check if virtual environment exists
        venv_path = self.backend_dir / ("venv" if not self.is_windows else "venv")
        if not venv_path.exists():
            self.print_colored("🔧 Creating Python virtual environment...", Colors.WARNING)
            try:
                subprocess.run([sys.executable, "-m", "venv", str(venv_path)], 
                             cwd=self.backend_dir, check=True, capture_output=True)
                self.print_colored("✅ Virtual environment created", Colors.OKGREEN)
            except subprocess.CalledProcessError as e:
                self.print_colored(f"❌ Failed to create virtual environment: {e}", Colors.FAIL)
                return False
                
        # Install dependencies
        pip_cmd = str(venv_path / ("Scripts" if self.is_windows else "bin") / "pip")
        try:
            self.print_colored("📦 Installing Python dependencies...", Colors.OKCYAN)
            subprocess.run([pip_cmd, "install", "-r", "requirements.txt"], 
                         cwd=self.backend_dir, check=True, capture_output=True)
            self.print_colored("✅ Backend dependencies installed", Colors.OKGREEN)
        except subprocess.CalledProcessError as e:
            self.print_colored(f"❌ Failed to install backend dependencies: {e}", Colors.FAIL)
            return False
            
        return True
        
    def install_frontend_dependencies(self):
        """Install frontend dependencies if needed"""
        self.print_colored("📦 Checking frontend dependencies...", Colors.OKCYAN)
        
        node_modules = self.frontend_dir / "node_modules"
        if not node_modules.exists():
            try:
                self.print_colored("📦 Installing Node.js dependencies...", Colors.OKCYAN)
                subprocess.run(["npm", "install"], cwd=self.frontend_dir, check=True, capture_output=True)
                self.print_colored("✅ Frontend dependencies installed", Colors.OKGREEN)
            except subprocess.CalledProcessError as e:
                self.print_colored(f"❌ Failed to install frontend dependencies: {e}", Colors.FAIL)
                return False
        else:
            self.print_colored("✅ Frontend dependencies already installed", Colors.OKGREEN)
            
        return True
        
    def start_backend(self):
        """Start the FastAPI backend server"""
        self.print_colored("🚀 Starting FastAPI backend server...", Colors.OKCYAN)
        
        try:
            # Determine Python executable path
            venv_path = self.backend_dir / "venv"
            if self.is_windows:
                python_cmd = str(venv_path / "Scripts" / "python.exe")
            else:
                python_cmd = str(venv_path / "bin" / "python")
                
            # If venv doesn't exist, use system Python
            if not Path(python_cmd).exists():
                python_cmd = sys.executable
                
            # Start backend process
            if self.is_windows:
                self.backend_process = subprocess.Popen(
                    [python_cmd, "main.py"],
                    cwd=self.backend_dir,
                    creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
                )
            else:
                self.backend_process = subprocess.Popen(
                    [python_cmd, "main.py"],
                    cwd=self.backend_dir,
                    preexec_fn=os.setsid
                )
                
            # Wait a moment and check if process started successfully
            time.sleep(2)
            if self.backend_process.poll() is None:
                self.print_colored("✅ Backend server started successfully on http://localhost:8000", Colors.OKGREEN)
                self.print_colored("📚 API Documentation: http://localhost:8000/docs", Colors.OKBLUE)
                return True
            else:
                self.print_colored("❌ Backend server failed to start", Colors.FAIL)
                return False
                
        except Exception as e:
            self.print_colored(f"❌ Error starting backend: {e}", Colors.FAIL)
            return False
            
    def start_frontend(self):
        """Start the Next.js frontend server"""
        self.print_colored("🚀 Starting Next.js frontend server...", Colors.OKCYAN)
        
        try:
            # Start frontend process
            if self.is_windows:
                self.frontend_process = subprocess.Popen(
                    ["npm", "run", "dev"],
                    cwd=self.frontend_dir,
                    creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
                )
            else:
                self.frontend_process = subprocess.Popen(
                    ["npm", "run", "dev"],
                    cwd=self.frontend_dir,
                    preexec_fn=os.setsid
                )
                
            # Wait a moment and check if process started successfully
            time.sleep(3)
            if self.frontend_process.poll() is None:
                self.print_colored("✅ Frontend server started successfully on http://localhost:3000", Colors.OKGREEN)
                return True
            else:
                self.print_colored("❌ Frontend server failed to start", Colors.FAIL)
                return False
                
        except Exception as e:
            self.print_colored(f"❌ Error starting frontend: {e}", Colors.FAIL)
            return False
            
    def wait_for_backend(self, timeout=30):
        """Wait for backend to be ready"""
        import urllib.request
        import urllib.error
        
        self.print_colored("⏳ Waiting for backend to be ready...", Colors.OKCYAN)
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                urllib.request.urlopen("http://localhost:8000", timeout=1)
                self.print_colored("✅ Backend is ready!", Colors.OKGREEN)
                return True
            except (urllib.error.URLError, ConnectionError):
                time.sleep(1)
                
        self.print_colored("❌ Backend failed to become ready within timeout", Colors.FAIL)
        return False
        
    def cleanup(self):
        """Clean up processes on exit"""
        self.print_colored("\n🛑 Shutting down servers...", Colors.WARNING)
        
        if self.frontend_process:
            try:
                if self.is_windows:
                    self.frontend_process.send_signal(signal.CTRL_BREAK_EVENT)
                else:
                    os.killpg(os.getpgid(self.frontend_process.pid), signal.SIGTERM)
                self.frontend_process.wait(timeout=5)
                self.print_colored("✅ Frontend server stopped", Colors.OKGREEN)
            except Exception as e:
                self.print_colored(f"⚠️  Error stopping frontend: {e}", Colors.WARNING)
                
        if self.backend_process:
            try:
                if self.is_windows:
                    self.backend_process.send_signal(signal.CTRL_BREAK_EVENT)
                else:
                    os.killpg(os.getpgid(self.backend_process.pid), signal.SIGTERM)
                self.backend_process.wait(timeout=5)
                self.print_colored("✅ Backend server stopped", Colors.OKGREEN)
            except Exception as e:
                self.print_colored(f"⚠️  Error stopping backend: {e}", Colors.WARNING)
                
        self.print_colored("👋 Goodbye!", Colors.HEADER)
        
    def run(self):
        """Main execution method"""
        try:
            self.print_banner()
            
            # Check prerequisites
            if not self.check_prerequisites():
                sys.exit(1)
                
            # Check ports
            if not self.check_ports():
                sys.exit(1)
                
            # Install dependencies
            if not self.install_backend_dependencies():
                sys.exit(1)
                
            if not self.install_frontend_dependencies():
                sys.exit(1)
                
            # Start backend first
            if not self.start_backend():
                sys.exit(1)
                
            # Wait for backend to be ready
            if not self.wait_for_backend():
                self.cleanup()
                sys.exit(1)
                
            # Start frontend
            if not self.start_frontend():
                self.cleanup()
                sys.exit(1)
                
            # Success message
            self.print_colored("\n🎉 Both servers are running successfully!", Colors.OKGREEN)
            self.print_colored("🌐 Frontend: http://localhost:3000", Colors.OKBLUE)
            self.print_colored("🔧 Backend:  http://localhost:8000", Colors.OKBLUE)
            self.print_colored("📚 API Docs: http://localhost:8000/docs", Colors.OKBLUE)
            self.print_colored("\n💡 Press Ctrl+C to stop both servers", Colors.WARNING)
            
            # Keep script running and monitor processes
            while True:
                time.sleep(1)
                
                # Check if processes are still running
                if self.backend_process and self.backend_process.poll() is not None:
                    self.print_colored("❌ Backend process died unexpectedly", Colors.FAIL)
                    break
                    
                if self.frontend_process and self.frontend_process.poll() is not None:
                    self.print_colored("❌ Frontend process died unexpectedly", Colors.FAIL)
                    break
                    
        except KeyboardInterrupt:
            self.print_colored("\n⚠️  Received interrupt signal", Colors.WARNING)
        except Exception as e:
            self.print_colored(f"\n❌ Unexpected error: {e}", Colors.FAIL)
        finally:
            self.cleanup()

def signal_handler(signum, frame):
    """Handle interrupt signals"""
    print("\nReceived interrupt signal, shutting down...")
    sys.exit(0)

if __name__ == "__main__":
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    if not platform.system() == "Windows":
        signal.signal(signal.SIGTERM, signal_handler)
    
    # Run the server manager
    manager = ServerManager()
    manager.run()
