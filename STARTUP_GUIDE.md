# 🚀 My Shop - Server Startup Guide

This guide explains how to start both the FastAPI backend and Next.js frontend servers simultaneously using the provided startup scripts.

## 📋 Available Startup Scripts

### 1. **start-servers.py** (Recommended)
- **Cross-platform Python script** (Windows, macOS, Linux)
- **Full-featured** with dependency management, error handling, and monitoring
- **Automatic setup** of virtual environments and dependencies
- **Colored output** and progress indicators
- **Graceful shutdown** handling

### 2. **start-servers.bat** (Windows Only)
- **Windows Batch script** for Windows users
- **Simple and reliable** for Windows environments
- **Opens separate windows** for each server
- **Basic dependency installation**

### 3. **start-servers.sh** (Unix/Linux/macOS Only)
- **Shell script** for Unix-based systems
- **Full-featured** with monitoring and cleanup
- **Background process management**
- **Signal handling** for graceful shutdown

### 4. **start-servers-simple.py** (Lightweight)
- **Minimal Python script** with fewer dependencies
- **Quick startup** without extensive checks
- **Cross-platform** compatibility
- **Basic functionality** only

## 🚀 Quick Start

### Option 1: Python Script (Recommended)

```bash
# Make sure you're in the project root directory (my-shop/)
python start-servers.py
```

### Option 2: Platform-Specific Scripts

**Windows:**
```cmd
# Double-click the file or run from command prompt
start-servers.bat
```

**Unix/Linux/macOS:**
```bash
# Make the script executable first
chmod +x start-servers.sh

# Run the script
./start-servers.sh
```

### Option 3: Simple Python Script

```bash
# For a lightweight startup
python start-servers-simple.py
```

## 📋 Prerequisites

Before running any startup script, ensure you have:

### Required Software
- **Python 3.8+** (for backend)
- **Node.js 18+** (for frontend)
- **npm** (comes with Node.js)
- **MariaDB** server running on localhost:3308

### Database Setup
Make sure MariaDB is running with these settings:
- **Host:** localhost
- **Port:** 3308
- **Database:** mydatabase
- **Username:** root
- **Password:** 123456

Create the database if it doesn't exist:
```sql
CREATE DATABASE mydatabase;
```

## 🔧 Making Scripts Executable

### Windows
Scripts are executable by default. You can:
- Double-click `start-servers.bat`
- Run from Command Prompt: `start-servers.bat`
- Run Python script: `python start-servers.py`

### Unix/Linux/macOS
Make shell scripts executable:
```bash
chmod +x start-servers.sh
chmod +x start-servers.py  # Optional, for direct execution
```

## 📖 Detailed Usage Instructions

### Using start-servers.py (Full-Featured)

1. **Navigate to project root:**
   ```bash
   cd path/to/my-shop
   ```

2. **Run the script:**
   ```bash
   python start-servers.py
   ```

3. **What the script does:**
   - ✅ Checks all prerequisites
   - ✅ Verifies port availability
   - ✅ Creates Python virtual environment (if needed)
   - ✅ Installs backend dependencies
   - ✅ Installs frontend dependencies
   - ✅ Starts backend server (port 8000)
   - ✅ Waits for backend to be ready
   - ✅ Starts frontend server (port 3000)
   - ✅ Monitors both processes
   - ✅ Handles graceful shutdown on Ctrl+C

4. **Expected output:**
   ```
   ╔══════════════════════════════════════════════════════════════╗
   ║                        MY SHOP                               ║
   ║                Development Server Manager                    ║
   ║                                                              ║
   ║  Backend (FastAPI):  http://localhost:8000                  ║
   ║  Frontend (Next.js): http://localhost:3000                  ║
   ║  API Docs:          http://localhost:8000/docs              ║
   ╚══════════════════════════════════════════════════════════════╝

   🔍 Checking prerequisites...
   ✅ All prerequisites met
   🔍 Checking port availability...
   ✅ Ports are available
   📦 Checking backend dependencies...
   ✅ Backend dependencies installed
   📦 Checking frontend dependencies...
   ✅ Frontend dependencies installed
   🚀 Starting FastAPI backend server...
   ⏳ Waiting for backend to be ready...
   ✅ Backend is ready!
   ✅ Backend server started successfully on http://localhost:8000
   📚 API Documentation: http://localhost:8000/docs
   🚀 Starting Next.js frontend server...
   ✅ Frontend server started successfully on http://localhost:3000

   🎉 Both servers are running successfully!
   🌐 Frontend: http://localhost:3000
   🔧 Backend:  http://localhost:8000
   📚 API Docs: http://localhost:8000/docs

   💡 Press Ctrl+C to stop both servers
   ```

### Using start-servers.bat (Windows)

1. **Open Command Prompt in project root**
2. **Run:** `start-servers.bat`
3. **Two new windows will open:**
   - One for the backend server
   - One for the frontend server
4. **Close the windows or press Ctrl+C in them to stop servers**

### Using start-servers.sh (Unix/Linux/macOS)

1. **Make executable:** `chmod +x start-servers.sh`
2. **Run:** `./start-servers.sh`
3. **Servers run in background**
4. **Press Ctrl+C to stop both servers**

## 🌐 Accessing the Application

Once both servers are running:

### Frontend (User Interface)
- **URL:** http://localhost:3000
- **Features:** Product catalog, shopping cart, user authentication

### Backend (API)
- **URL:** http://localhost:8000
- **API Documentation:** http://localhost:8000/docs
- **Interactive API:** http://localhost:8000/redoc

### Default Login Credentials
- **Admin:** <EMAIL> / admin123
- **User:** <EMAIL> / password123

## 🛑 Stopping the Servers

### Method 1: Keyboard Interrupt
Press **Ctrl+C** in the terminal where the script is running.

### Method 2: Close Terminal/Command Prompt
Simply close the terminal window.

### Method 3: Manual Process Termination
If servers don't stop gracefully:

**Windows:**
```cmd
# Find and kill processes
tasklist | findstr "python\|node"
taskkill /F /PID <process_id>
```

**Unix/Linux/macOS:**
```bash
# Find and kill processes
ps aux | grep -E "(python|node)"
kill -9 <process_id>

# Or kill by port
lsof -ti:8000 | xargs kill -9  # Backend
lsof -ti:3000 | xargs kill -9  # Frontend
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Port Already in Use
**Error:** Port 8000 or 3000 is already in use
**Solution:**
- Stop other applications using these ports
- Or modify the ports in the configuration files

#### 2. Python Not Found
**Error:** 'python' is not recognized
**Solution:**
- Install Python 3.8+ from python.org
- Add Python to your system PATH
- Try `python3` instead of `python`

#### 3. Node.js Not Found
**Error:** 'node' or 'npm' is not recognized
**Solution:**
- Install Node.js 18+ from nodejs.org
- Restart your terminal after installation

#### 4. Database Connection Error
**Error:** Can't connect to MariaDB
**Solution:**
- Ensure MariaDB is running on localhost:3308
- Create the database: `CREATE DATABASE mydatabase;`
- Check credentials in `be/app/core/config.py`

#### 5. Permission Denied (Unix/Linux/macOS)
**Error:** Permission denied when running shell script
**Solution:**
```bash
chmod +x start-servers.sh
```

#### 6. Virtual Environment Issues
**Error:** Virtual environment creation fails
**Solution:**
```bash
# Install venv module
python -m pip install --user virtualenv

# Or use system Python
python -m venv venv
```

### Debug Mode

To run with more verbose output:

**Python script:**
```bash
# Set debug environment variable
DEBUG=1 python start-servers.py
```

**Check logs:**
- Backend logs: `be/backend.log` (if created)
- Frontend logs: `fe/frontend.log` (if created)

## 📁 Project Structure Verification

Ensure your project structure looks like this:
```
my-shop/
├── be/                     # Backend directory
│   ├── main.py            # FastAPI entry point
│   ├── requirements.txt   # Python dependencies
│   └── app/               # Application code
├── fe/                     # Frontend directory
│   ├── package.json       # Node.js dependencies
│   ├── src/               # Source code
│   └── public/            # Static assets
├── start-servers.py       # Main startup script
├── start-servers.bat      # Windows batch script
├── start-servers.sh       # Unix shell script
├── start-servers-simple.py # Simple Python script
└── README.md              # Project documentation
```

## 🚀 Advanced Usage

### Custom Configuration

You can modify the scripts to:
- Change default ports
- Add environment variables
- Customize startup behavior
- Add additional services

### Environment Variables

Set these before running scripts:
```bash
# Custom API URL for frontend
export NEXT_PUBLIC_API_URL=http://localhost:8000

# Custom database URL for backend
export DATABASE_URL=mysql+pymysql://user:pass@localhost:3308/mydb
```

### Running in Production

For production deployment:
1. Use proper process managers (PM2, systemd, etc.)
2. Set up reverse proxy (nginx, Apache)
3. Configure SSL certificates
4. Use production databases
5. Set proper environment variables

## 📞 Support

If you encounter issues:

1. **Check Prerequisites:** Ensure all required software is installed
2. **Verify Project Structure:** Make sure you're in the correct directory
3. **Check Ports:** Ensure ports 3000 and 8000 are available
4. **Review Logs:** Check console output for error messages
5. **Database Connection:** Verify MariaDB is running and accessible

For additional help, refer to:
- Main project README.md
- Backend documentation (be/README.md)
- Frontend documentation (fe/README.md)
- API documentation at http://localhost:8000/docs
