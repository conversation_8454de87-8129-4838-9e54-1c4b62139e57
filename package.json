{"name": "my-shop", "version": "1.0.0", "description": "Complete e-commerce website with Next.js frontend and FastAPI backend", "scripts": {"start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:backend": "cd be && python main.py", "start:frontend": "cd fe && npm run dev", "install:all": "cd be && pip install -r requirements.txt && cd ../fe && npm install", "setup": "cd be && python create_sample_data.py"}, "devDependencies": {"concurrently": "^8.2.2"}, "keywords": ["ecommerce", "nextjs", "<PERSON><PERSON><PERSON>", "react", "python"], "author": "My Shop", "license": "MIT"}