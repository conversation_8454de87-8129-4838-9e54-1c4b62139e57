#!/usr/bin/env python3
"""
My Shop - Simple Cross-Platform Server Startup Script
A lightweight version that starts both servers with minimal dependencies
"""

import os
import sys
import subprocess
import signal
import time
import platform
from pathlib import Path

def main():
    """Simple server startup script"""
    is_windows = platform.system() == "Windows"
    project_root = Path(__file__).parent.absolute()
    backend_dir = project_root / "be"
    frontend_dir = project_root / "fe"
    
    print("=" * 60)
    print("MY SHOP - Development Server Startup")
    print("=" * 60)
    print("Backend:  http://localhost:8000")
    print("Frontend: http://localhost:3000")
    print("API Docs: http://localhost:8000/docs")
    print("=" * 60)
    
    # Check directories
    if not backend_dir.exists() or not frontend_dir.exists():
        print("❌ Error: 'be' or 'fe' directory not found!")
        print("Please run this script from the project root directory.")
        sys.exit(1)
    
    backend_process = None
    frontend_process = None
    
    try:
        # Start backend
        print("🚀 Starting backend server...")
        os.chdir(backend_dir)
        
        # Try to use virtual environment Python, fallback to system Python
        python_cmd = "python"
        venv_python = backend_dir / ("venv/Scripts/python.exe" if is_windows else "venv/bin/python")
        if venv_python.exists():
            python_cmd = str(venv_python)
        
        backend_process = subprocess.Popen([python_cmd, "main.py"])
        
        # Wait for backend to start
        print("⏳ Waiting for backend...")
        time.sleep(5)
        
        # Start frontend
        print("🚀 Starting frontend server...")
        os.chdir(frontend_dir)
        frontend_process = subprocess.Popen(["npm", "run", "dev"])
        
        print("\n✅ Both servers started!")
        print("🌐 Frontend: http://localhost:3000")
        print("🔧 Backend:  http://localhost:8000")
        print("\n💡 Press Ctrl+C to stop both servers\n")
        
        # Wait for processes
        while True:
            time.sleep(1)
            if backend_process.poll() is not None:
                print("❌ Backend process stopped")
                break
            if frontend_process.poll() is not None:
                print("❌ Frontend process stopped")
                break
                
    except KeyboardInterrupt:
        print("\n🛑 Stopping servers...")
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        # Cleanup
        if frontend_process:
            frontend_process.terminate()
            try:
                frontend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                frontend_process.kill()
        
        if backend_process:
            backend_process.terminate()
            try:
                backend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                backend_process.kill()
        
        print("✅ Servers stopped. Goodbye!")

if __name__ == "__main__":
    main()
